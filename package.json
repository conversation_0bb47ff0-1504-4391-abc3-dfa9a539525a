{"name": "link-agent", "version": "1.0.0", "description": "企业经营全流程管理系统 - 以智能体为核心的MCP统一协调平台", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:mcp\" \"npm run dev:lead\" \"npm run dev:sales\" \"npm run dev:web\"", "dev:mcp": "cd packages/mcp-core && npm run dev", "dev:lead": "cd packages/lead-agent && npm run dev", "dev:sales": "cd packages/sales-agent && npm run dev", "dev:project": "cd packages/project-agent && npm run dev", "dev:finance": "cd packages/finance-agent && npm run dev", "dev:bom": "cd packages/bom-agent && npm run dev", "dev:service": "cd packages/service-agent && npm run dev", "dev:analytics": "cd packages/analytics-agent && npm run dev", "dev:web": "cd web && npm run dev", "dev:mobile": "cd mobile && npm run dev", "build": "npm run build:packages && npm run build:web", "build:packages": "npm run build:mcp && npm run build:agents", "build:mcp": "cd packages/mcp-core && npm run build", "build:agents": "concurrently \"cd packages/lead-agent && npm run build\" \"cd packages/sales-agent && npm run build\" \"cd packages/project-agent && npm run build\" \"cd packages/finance-agent && npm run build\" \"cd packages/bom-agent && npm run build\" \"cd packages/service-agent && npm run build\" \"cd packages/analytics-agent && npm run build\"", "build:web": "cd web && npm run build", "build:mobile": "cd mobile && npm run build", "test": "npm run test:packages && npm run test:web", "test:packages": "concurrently \"cd packages/mcp-core && npm test\" \"cd packages/lead-agent && npm test\" \"cd packages/sales-agent && npm test\"", "test:web": "cd web && npm test", "test:e2e": "cd tests && npm run test:e2e", "lint": "npm run lint:packages && npm run lint:web", "lint:packages": "concurrently \"cd packages/mcp-core && npm run lint\" \"cd packages/lead-agent && npm run lint\" \"cd packages/sales-agent && npm run lint\"", "lint:web": "cd web && npm run lint", "lint:fix": "npm run lint:packages:fix && npm run lint:web:fix", "lint:packages:fix": "concurrently \"cd packages/mcp-core && npm run lint:fix\" \"cd packages/lead-agent && npm run lint:fix\" \"cd packages/sales-agent && npm run lint:fix\"", "lint:web:fix": "cd web && npm run lint:fix", "install:all": "npm install && npm run install:packages && npm run install:web && npm run install:mobile", "install:packages": "concurrently \"cd packages/mcp-core && npm install\" \"cd packages/lead-agent && npm install\" \"cd packages/sales-agent && npm install\" \"cd packages/project-agent && npm install\" \"cd packages/finance-agent && npm install\" \"cd packages/bom-agent && npm install\" \"cd packages/service-agent && npm install\" \"cd packages/analytics-agent && npm install\" \"cd packages/shared && npm install\"", "install:web": "cd web && npm install", "install:mobile": "cd mobile && npm install", "db:migrate": "cd packages/shared && npm run db:migrate", "db:seed": "cd packages/shared && npm run db:seed", "db:reset": "cd packages/shared && npm run db:reset", "db:check": "cd packages/shared && npm run db:check", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "k8s:deploy": "kubectl apply -f infrastructure/kubernetes/", "k8s:delete": "kubectl delete -f infrastructure/kubernetes/", "clean": "npm run clean:packages && npm run clean:web && npm run clean:mobile", "clean:packages": "concurrently \"cd packages/mcp-core && rm -rf dist node_modules\" \"cd packages/lead-agent && rm -rf dist node_modules\" \"cd packages/sales-agent && rm -rf dist node_modules\"", "clean:web": "cd web && rm -rf dist node_modules", "clean:mobile": "cd mobile && rm -rf dist node_modules", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "type-check": "npm run type-check:packages && npm run type-check:web", "type-check:packages": "concurrently \"cd packages/mcp-core && npm run type-check\" \"cd packages/lead-agent && npm run type-check\"", "type-check:web": "cd web && npm run type-check", "prepare": "husky install", "postinstall": "npm run install:all"}, "keywords": ["enterprise", "management", "ai", "agent", "mcp", "crm", "erp", "workflow", "automation"], "author": "Link Agent Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/link-agent.git"}, "bugs": {"url": "https://github.com/your-org/link-agent/issues"}, "homepage": "https://github.com/your-org/link-agent#readme", "workspaces": ["packages/*", "web", "mobile"], "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.0", "lint-staged": "^14.0.0", "prettier": "^3.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run type-check && npm test"}}}
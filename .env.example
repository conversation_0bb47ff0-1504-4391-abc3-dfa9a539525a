# 环境配置
NODE_ENV=development
LOG_LEVEL=debug

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USER=admin
DB_PASSWORD=password
DB_NAME=linkagent
DATABASE_URL=postgresql://admin:password@localhost:5432/linkagent

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_URL=redis://localhost:6379

# MongoDB配置
MONGO_HOST=localhost
MONGO_PORT=27017
MONGO_USER=admin
MONGO_PASSWORD=password
MONGO_DB=linkagent
MONGODB_URL=**************************************************

# RabbitMQ配置
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=password
RABBITMQ_MANAGEMENT_PORT=15672
RABBITMQ_URL=amqp://admin:password@localhost:5672

# Elasticsearch配置
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_URL=http://localhost:9200

# Kibana配置
KIBANA_PORT=5601

# Prometheus配置
PROMETHEUS_PORT=9090

# Grafana配置
GRAFANA_PORT=3001
GRAFANA_USER=admin
GRAFANA_PASSWORD=admin

# Jaeger配置
JAEGER_UI_PORT=16686
JAEGER_COLLECTOR_PORT=14268
JAEGER_OTLP_PORT=4317

# MinIO配置
MINIO_API_PORT=9000
MINIO_CONSOLE_PORT=9001
MINIO_USER=admin
MINIO_PASSWORD=password123

# 应用服务端口
MCP_PORT=3000
WEB_PORT=3002
LEAD_AGENT_PORT=3010
SALES_AGENT_PORT=3011
PROJECT_AGENT_PORT=3012
FINANCE_AGENT_PORT=3013
BOM_AGENT_PORT=3014
SERVICE_AGENT_PORT=3015
ANALYTICS_AGENT_PORT=3016

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# 加密配置
ENCRYPTION_KEY=your-32-character-encryption-key
HASH_SALT_ROUNDS=12

# AI服务配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4
OPENAI_EMBEDDING_MODEL=text-embedding-ada-002
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.7

# 向量数据库配置 (Pinecone)
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=us-west1-gcp
PINECONE_INDEX_NAME=linkagent-embeddings

# 语音识别配置 (Azure Speech)
AZURE_SPEECH_KEY=your-azure-speech-key
AZURE_SPEECH_REGION=eastus

# OCR配置 (Azure Computer Vision)
AZURE_VISION_KEY=your-azure-vision-key
AZURE_VISION_ENDPOINT=https://your-region.api.cognitive.microsoft.com/

# 邮件服务配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=<EMAIL>

# 短信服务配置 (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# 文件存储配置
FILE_STORAGE_TYPE=local # local, s3, minio
FILE_UPLOAD_MAX_SIZE=******** # 10MB
FILE_ALLOWED_TYPES=jpg,jpeg,png,pdf,doc,docx,xls,xlsx

# AWS S3配置 (如果使用S3)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=linkagent-files

# 微信小程序配置
WECHAT_APP_ID=your-wechat-app-id
WECHAT_APP_SECRET=your-wechat-app-secret

# 钉钉配置
DINGTALK_APP_KEY=your-dingtalk-app-key
DINGTALK_APP_SECRET=your-dingtalk-app-secret

# 企业微信配置
WEWORK_CORP_ID=your-wework-corp-id
WEWORK_CORP_SECRET=your-wework-corp-secret

# 支付配置
# 微信支付
WECHAT_PAY_MCHID=your-wechat-pay-mchid
WECHAT_PAY_PRIVATE_KEY=your-wechat-pay-private-key
WECHAT_PAY_CERT_SERIAL=your-wechat-pay-cert-serial

# 支付宝
ALIPAY_APP_ID=your-alipay-app-id
ALIPAY_PRIVATE_KEY=your-alipay-private-key
ALIPAY_PUBLIC_KEY=your-alipay-public-key

# 监控配置
SENTRY_DSN=your-sentry-dsn
NEW_RELIC_LICENSE_KEY=your-newrelic-license-key

# 缓存配置
CACHE_TTL=3600 # 1小时
CACHE_MAX_SIZE=1000

# 限流配置
RATE_LIMIT_WINDOW=900000 # 15分钟
RATE_LIMIT_MAX_REQUESTS=100

# 会话配置
SESSION_SECRET=your-session-secret
SESSION_MAX_AGE=86400000 # 24小时

# CORS配置
CORS_ORIGIN=http://localhost:3002,http://localhost:3000
CORS_CREDENTIALS=true

# API配置
API_VERSION=v1
API_PREFIX=/api
API_TIMEOUT=30000 # 30秒

# 工作流配置
WORKFLOW_ENGINE=camunda # camunda, zeebe
CAMUNDA_REST_URL=http://localhost:8080/engine-rest

# MCP协议配置
MCP_SERVER_PORT=8080
MCP_CLIENT_TIMEOUT=30000
MCP_MAX_CONNECTIONS=1000

# 智能设备配置
XIAOMI_APP_ID=your-xiaomi-app-id
XIAOMI_APP_SECRET=your-xiaomi-app-secret

# 开发工具配置
DEBUG=linkagent:*
SWAGGER_ENABLED=true
MOCK_DATA_ENABLED=true

# 测试配置
TEST_DATABASE_URL=postgresql://admin:password@localhost:5432/linkagent_test
TEST_REDIS_URL=redis://localhost:6379/1

# 部署配置
DOCKER_REGISTRY=your-docker-registry.com
K8S_NAMESPACE=linkagent
HELM_CHART_VERSION=1.0.0

# 备份配置
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * * # 每天凌晨2点
BACKUP_RETENTION_DAYS=30

# 安全配置
SECURITY_HEADERS_ENABLED=true
CSRF_PROTECTION_ENABLED=true
XSS_PROTECTION_ENABLED=true

# 国际化配置
DEFAULT_LOCALE=zh-CN
SUPPORTED_LOCALES=zh-CN,en-US,ja-JP

# 时区配置
DEFAULT_TIMEZONE=Asia/Shanghai

# 功能开关
FEATURE_AI_ASSISTANT=true
FEATURE_VOICE_INPUT=true
FEATURE_OCR_RECOGNITION=true
FEATURE_SMART_RECOMMENDATION=true
FEATURE_REAL_TIME_COLLABORATION=true

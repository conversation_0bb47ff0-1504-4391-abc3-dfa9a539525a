# Link Agent - 企业经营全流程管理系统

## 项目概述

Link Agent 是一个以智能体（Agent）为核心、由主控平台（MCP）统一协调的企业经营全流程管理系统。系统将打通从市场线索到售后服务的每一个环节，利用AI技术实现流程自动化、决策智能化、数据资产化。

## 核心特性

- 🤖 **智能体架构**: 8个专业智能体协同工作
- 🎯 **统一主控**: MCP平台统一协调和数据管理
- 🧠 **AI驱动**: 集成大语言模型和机器学习能力
- 📱 **多端支持**: Web、移动端、智能设备全覆盖
- 🔗 **MCP协议**: 支持智能音箱、智能屏幕等设备接入
- 🌐 **微服务架构**: 云原生、高可用、易扩展

## 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    主控平台 (MCP)                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ 用户认证    │ │ 权限管理    │ │ API网关     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ 工作流引擎  │ │ 消息队列    │ │ 数据总线    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
┌───────▼──────┐    ┌────────▼──────┐    ┌────────▼──────┐
│ 线索洞察智能体 │    │ 销售合同智能体 │    │ 项目执行智能体 │
└──────────────┘    └───────────────┘    └───────────────┘
        │                     │                     │
┌───────▼──────┐    ┌────────▼──────┐    ┌────────▼──────┐
│BOM供应链智能体│    │ 售后服务智能体 │    │ 财务结算智能体 │
└──────────────┘    └───────────────┘    └───────────────┘
        │                     │                     │
        └─────────────────────▼─────────────────────┘
                    ┌────────────────┐
                    │数据分析决策智能体│
                    └────────────────┘
```

## 技术栈

### 后端技术
- **框架**: Node.js + Express.js / Python + FastAPI
- **数据库**: PostgreSQL (主库) + Redis (缓存) + MongoDB (文档)
- **消息队列**: RabbitMQ / Apache Kafka
- **搜索引擎**: Elasticsearch
- **向量数据库**: Pinecone / Weaviate
- **容器化**: Docker + Kubernetes

### 前端技术
- **Web框架**: React.js + TypeScript
- **UI组件库**: Ant Design / Material-UI
- **状态管理**: Redux Toolkit / Zustand
- **构建工具**: Vite / Webpack
- **移动端**: React Native / Flutter

### AI与集成
- **大语言模型**: OpenAI GPT-4 / Claude / 本地部署模型
- **机器学习**: TensorFlow / PyTorch
- **MCP协议**: 自研MCP Server/Client
- **语音识别**: Azure Speech / Google Speech API
- **OCR**: Tesseract / Azure Computer Vision

### 基础设施
- **云平台**: AWS / Azure / 阿里云
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack (Elasticsearch + Logstash + Kibana)
- **CI/CD**: GitHub Actions / GitLab CI
- **安全**: OAuth 2.0 + JWT + HTTPS

## 项目结构

```
link-agent/
├── docs/                    # 项目文档
├── packages/               # 微服务包
│   ├── mcp-core/          # 主控平台核心
│   ├── lead-agent/        # 线索洞察智能体
│   ├── sales-agent/       # 销售合同智能体
│   ├── project-agent/     # 项目执行智能体
│   ├── bom-agent/         # BOM供应链智能体
│   ├── service-agent/     # 售后服务智能体
│   ├── finance-agent/     # 财务结算智能体
│   ├── analytics-agent/   # 数据分析智能体
│   └── shared/            # 共享组件和工具
├── web/                   # Web前端应用
├── mobile/                # 移动端应用
├── infrastructure/        # 基础设施配置
├── scripts/               # 构建和部署脚本
└── tests/                 # 测试文件
```

## 开发阶段

### 第一阶段 (MVP - 3个月)
- [x] 项目架构设计与技术选型
- [ ] 主控平台（MCP）核心开发
- [ ] 线索洞察智能体开发
- [ ] 销售合同智能体开发

### 第二阶段 (核心功能 - 3个月)
- [ ] 项目执行智能体开发
- [ ] 财务结算智能体开发
- [ ] 数据分析与决策支持智能体开发

### 第三阶段 (完整功能 - 3个月)
- [ ] BOM与供应链智能体开发
- [ ] 售后服务智能体开发
- [ ] AI能力集成与MCP协议实现

### 第四阶段 (生态扩展 - 3个月)
- [ ] 移动端与多渠道接入开发
- [ ] 系统集成与部署

## 快速开始

### 环境要求
- Node.js 18+
- Python 3.9+
- Docker & Docker Compose
- PostgreSQL 14+
- Redis 6+

### 本地开发
```bash
# 克隆项目
git clone https://github.com/your-org/link-agent.git
cd link-agent

# 安装依赖
npm install

# 启动开发环境
docker-compose up -d
npm run dev
```

## 贡献指南

请参阅 [CONTRIBUTING.md](./CONTRIBUTING.md) 了解如何参与项目开发。

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](./LICENSE) 文件。

{"name": "@linkagent/shared", "version": "1.0.0", "description": "Link Agent共享组件和工具库", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "db:migrate": "knex migrate:latest", "db:rollback": "knex migrate:rollback", "db:seed": "knex seed:run", "db:reset": "knex migrate:rollback --all && knex migrate:latest && knex seed:run", "db:check": "node dist/database/check.js"}, "keywords": ["linkagent", "shared", "types", "utils", "database"], "author": "Link Agent Team", "license": "MIT", "dependencies": {"knex": "^3.0.0", "objection": "^3.1.0", "pg": "^8.11.0", "redis": "^4.6.0", "mongodb": "^6.0.0", "amqplib": "^0.10.0", "@elastic/elasticsearch": "^8.11.0", "winston": "^3.11.0", "joi": "^17.11.0", "bcrypt": "^5.1.0", "jsonwebtoken": "^9.0.0", "uuid": "^9.0.0", "dayjs": "^1.11.0", "lodash": "^4.17.21"}, "devDependencies": {"@types/node": "^20.0.0", "@types/bcrypt": "^5.0.0", "@types/jsonwebtoken": "^9.0.0", "@types/uuid": "^9.0.0", "@types/lodash": "^4.14.0", "@types/amqplib": "^0.10.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0", "ts-jest": "^29.0.0", "typescript": "^5.0.0"}, "peerDependencies": {"typescript": "^5.0.0"}}
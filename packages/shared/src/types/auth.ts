// 认证和授权相关类型定义

// JWT Token相关
export interface JwtPayload {
  sub: string; // 用户ID
  email: string;
  role: string;
  permissions: string[];
  iat: number; // 签发时间
  exp: number; // 过期时间
  iss: string; // 签发者
  aud: string; // 受众
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: 'Bearer';
}

export interface RefreshTokenPayload {
  sub: string;
  tokenId: string;
  iat: number;
  exp: number;
}

// 登录相关
export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
  captcha?: string;
  deviceInfo?: DeviceInfo;
}

export interface LoginResponse {
  user: AuthUser;
  tokens: TokenPair;
  firstLogin?: boolean;
  passwordExpired?: boolean;
  mfaRequired?: boolean;
}

export interface DeviceInfo {
  deviceId: string;
  deviceName: string;
  platform: string;
  browser?: string;
  ipAddress: string;
  userAgent: string;
}

// 注册相关
export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  inviteCode?: string;
  acceptTerms: boolean;
}

export interface RegisterResponse {
  user: AuthUser;
  tokens?: TokenPair;
  emailVerificationRequired?: boolean;
}

// 密码相关
export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface ResetPasswordRequest {
  email: string;
  captcha?: string;
}

export interface ResetPasswordConfirmRequest {
  token: string;
  newPassword: string;
}

// 多因子认证
export interface MfaSetupRequest {
  type: MfaType;
  phoneNumber?: string;
}

export interface MfaSetupResponse {
  secret?: string; // TOTP密钥
  qrCode?: string; // TOTP二维码
  backupCodes?: string[]; // 备用代码
}

export interface MfaVerifyRequest {
  type: MfaType;
  code: string;
  rememberDevice?: boolean;
}

export enum MfaType {
  TOTP = 'totp', // 时间基础的一次性密码
  SMS = 'sms',   // 短信验证码
  EMAIL = 'email', // 邮件验证码
  BACKUP = 'backup' // 备用代码
}

// 用户认证信息
export interface AuthUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  role: string;
  permissions: Permission[];
  status: string;
  emailVerified: boolean;
  mfaEnabled: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
}

export interface Permission {
  resource: string;
  actions: string[];
  conditions?: PermissionCondition[];
}

export interface PermissionCondition {
  field: string;
  operator: 'eq' | 'ne' | 'in' | 'nin' | 'contains';
  value: any;
}

// 会话管理
export interface Session {
  id: string;
  userId: string;
  deviceId: string;
  ipAddress: string;
  userAgent: string;
  createdAt: Date;
  lastAccessAt: Date;
  expiresAt: Date;
  isActive: boolean;
}

export interface SessionInfo {
  current: Session;
  others: Session[];
}

// OAuth相关
export interface OAuthProvider {
  name: string;
  clientId: string;
  authorizeUrl: string;
  tokenUrl: string;
  userInfoUrl: string;
  scopes: string[];
}

export interface OAuthAuthorizationRequest {
  provider: string;
  redirectUri: string;
  state?: string;
}

export interface OAuthCallbackRequest {
  provider: string;
  code: string;
  state?: string;
}

export interface OAuthUserInfo {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  provider: string;
}

// API密钥管理
export interface ApiKey {
  id: string;
  name: string;
  key: string;
  userId: string;
  permissions: string[];
  expiresAt?: Date;
  lastUsedAt?: Date;
  isActive: boolean;
  createdAt: Date;
}

export interface CreateApiKeyRequest {
  name: string;
  permissions: string[];
  expiresAt?: Date;
}

export interface CreateApiKeyResponse {
  apiKey: ApiKey;
  secret: string; // 只在创建时返回
}

// 角色和权限管理
export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions: Permission[];
  isSystem: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateRoleRequest {
  name: string;
  description?: string;
  permissions: Permission[];
}

export interface UpdateRoleRequest {
  name?: string;
  description?: string;
  permissions?: Permission[];
}

// 权限检查
export interface PermissionCheck {
  resource: string;
  action: string;
  context?: Record<string, any>;
}

export interface PermissionCheckResult {
  allowed: boolean;
  reason?: string;
  conditions?: PermissionCondition[];
}

// 审计日志
export interface AuthAuditLog {
  id: string;
  userId?: string;
  action: AuthAction;
  resource?: string;
  ipAddress: string;
  userAgent: string;
  success: boolean;
  error?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
}

export enum AuthAction {
  LOGIN = 'login',
  LOGOUT = 'logout',
  REGISTER = 'register',
  PASSWORD_CHANGE = 'password_change',
  PASSWORD_RESET = 'password_reset',
  MFA_SETUP = 'mfa_setup',
  MFA_VERIFY = 'mfa_verify',
  TOKEN_REFRESH = 'token_refresh',
  PERMISSION_CHECK = 'permission_check',
  ROLE_ASSIGN = 'role_assign',
  ROLE_REVOKE = 'role_revoke'
}

// 安全策略
export interface SecurityPolicy {
  passwordPolicy: PasswordPolicy;
  sessionPolicy: SessionPolicy;
  mfaPolicy: MfaPolicy;
  lockoutPolicy: LockoutPolicy;
}

export interface PasswordPolicy {
  minLength: number;
  maxLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  forbidCommonPasswords: boolean;
  maxAge: number; // 密码最大有效期（天）
  historyCount: number; // 不能重复使用的历史密码数量
}

export interface SessionPolicy {
  maxDuration: number; // 最大会话时长（秒）
  idleTimeout: number; // 空闲超时（秒）
  maxConcurrentSessions: number; // 最大并发会话数
  requireMfaForSensitiveActions: boolean;
}

export interface MfaPolicy {
  required: boolean;
  allowedMethods: MfaType[];
  gracePeriod: number; // 宽限期（天）
  backupCodesCount: number;
}

export interface LockoutPolicy {
  enabled: boolean;
  maxAttempts: number;
  lockoutDuration: number; // 锁定时长（秒）
  resetAfter: number; // 重置计数器时间（秒）
}

// 设备管理
export interface TrustedDevice {
  id: string;
  userId: string;
  deviceId: string;
  deviceName: string;
  platform: string;
  browser?: string;
  ipAddress: string;
  trustedAt: Date;
  lastUsedAt: Date;
  expiresAt?: Date;
  isActive: boolean;
}

export interface TrustDeviceRequest {
  deviceInfo: DeviceInfo;
  duration?: number; // 信任时长（天）
}

// 邀请管理
export interface Invitation {
  id: string;
  email: string;
  role: string;
  invitedBy: string;
  expiresAt: Date;
  acceptedAt?: Date;
  createdAt: Date;
  status: InvitationStatus;
}

export enum InvitationStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled'
}

export interface CreateInvitationRequest {
  email: string;
  role: string;
  expiresIn?: number; // 有效期（小时）
  message?: string;
}

export interface AcceptInvitationRequest {
  token: string;
  password: string;
  firstName: string;
  lastName: string;
}

// 单点登录 (SSO)
export interface SsoProvider {
  id: string;
  name: string;
  type: SsoType;
  enabled: boolean;
  config: SsoConfig;
  createdAt: Date;
  updatedAt: Date;
}

export enum SsoType {
  SAML = 'saml',
  OIDC = 'oidc',
  OAUTH2 = 'oauth2'
}

export interface SsoConfig {
  // SAML配置
  entityId?: string;
  ssoUrl?: string;
  certificate?: string;
  
  // OIDC配置
  issuer?: string;
  clientId?: string;
  clientSecret?: string;
  
  // 通用配置
  attributeMapping?: Record<string, string>;
  autoCreateUsers?: boolean;
  defaultRole?: string;
}

export interface SsoLoginRequest {
  providerId: string;
  redirectUri?: string;
}

export interface SsoCallbackRequest {
  providerId: string;
  token: string;
  state?: string;
}

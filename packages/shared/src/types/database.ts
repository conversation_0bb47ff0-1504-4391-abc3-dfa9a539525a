// 数据库相关类型定义

// 数据库连接配置
export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean | DatabaseSSLConfig;
  pool?: DatabasePoolConfig;
  migrations?: DatabaseMigrationConfig;
  seeds?: DatabaseSeedConfig;
}

export interface DatabaseSSLConfig {
  rejectUnauthorized?: boolean;
  ca?: string;
  cert?: string;
  key?: string;
}

export interface DatabasePoolConfig {
  min: number;
  max: number;
  acquireTimeoutMillis?: number;
  createTimeoutMillis?: number;
  destroyTimeoutMillis?: number;
  idleTimeoutMillis?: number;
  reapIntervalMillis?: number;
  createRetryIntervalMillis?: number;
}

export interface DatabaseMigrationConfig {
  directory: string;
  tableName?: string;
  schemaName?: string;
  disableTransactions?: boolean;
}

export interface DatabaseSeedConfig {
  directory: string;
}

// 查询构建器
export interface QueryBuilder<T = any> {
  select(columns?: string | string[]): QueryBuilder<T>;
  where(column: string, value: any): QueryBuilder<T>;
  where(column: string, operator: string, value: any): QueryBuilder<T>;
  where(conditions: Record<string, any>): QueryBuilder<T>;
  whereIn(column: string, values: any[]): QueryBuilder<T>;
  whereNotIn(column: string, values: any[]): QueryBuilder<T>;
  whereNull(column: string): QueryBuilder<T>;
  whereNotNull(column: string): QueryBuilder<T>;
  whereBetween(column: string, range: [any, any]): QueryBuilder<T>;
  whereRaw(sql: string, bindings?: any[]): QueryBuilder<T>;
  join(table: string, first: string, operator: string, second: string): QueryBuilder<T>;
  leftJoin(table: string, first: string, operator: string, second: string): QueryBuilder<T>;
  rightJoin(table: string, first: string, operator: string, second: string): QueryBuilder<T>;
  innerJoin(table: string, first: string, operator: string, second: string): QueryBuilder<T>;
  groupBy(columns: string | string[]): QueryBuilder<T>;
  having(column: string, operator: string, value: any): QueryBuilder<T>;
  orderBy(column: string, direction?: 'asc' | 'desc'): QueryBuilder<T>;
  limit(count: number): QueryBuilder<T>;
  offset(count: number): QueryBuilder<T>;
  distinct(columns?: string | string[]): QueryBuilder<T>;
  count(column?: string): QueryBuilder<number>;
  sum(column: string): QueryBuilder<number>;
  avg(column: string): QueryBuilder<number>;
  min(column: string): QueryBuilder<any>;
  max(column: string): QueryBuilder<any>;
  first(): Promise<T | undefined>;
  find(id: any): Promise<T | undefined>;
  findOrFail(id: any): Promise<T>;
  get(): Promise<T[]>;
  paginate(page: number, limit: number): Promise<PaginatedResult<T>>;
  insert(data: Partial<T>): Promise<T>;
  update(data: Partial<T>): Promise<number>;
  delete(): Promise<number>;
  clone(): QueryBuilder<T>;
}

export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 事务管理
export interface Transaction {
  commit(): Promise<void>;
  rollback(): Promise<void>;
  savepoint(name: string): Promise<void>;
  rollbackTo(name: string): Promise<void>;
  isCompleted(): boolean;
}

export interface TransactionOptions {
  isolationLevel?: IsolationLevel;
  readOnly?: boolean;
  timeout?: number;
}

export enum IsolationLevel {
  READ_UNCOMMITTED = 'READ UNCOMMITTED',
  READ_COMMITTED = 'READ COMMITTED',
  REPEATABLE_READ = 'REPEATABLE READ',
  SERIALIZABLE = 'SERIALIZABLE'
}

// 数据库迁移
export interface Migration {
  up(knex: any): Promise<void>;
  down(knex: any): Promise<void>;
}

export interface MigrationInfo {
  name: string;
  batch: number;
  migrationTime: Date;
}

export interface MigrationStatus {
  current: string;
  pending: string[];
  completed: MigrationInfo[];
}

// 数据库种子
export interface Seed {
  seed(knex: any): Promise<void>;
}

// 模型定义
export interface ModelClass<T = any> {
  tableName: string;
  idColumn?: string | string[];
  jsonSchema?: any;
  relationMappings?: any;
  modifiers?: Record<string, (query: QueryBuilder) => void>;
  virtualAttributes?: string[];
  
  query(): QueryBuilder<T>;
  knex(): any;
  transaction<R>(callback: (trx: Transaction) => Promise<R>): Promise<R>;
  
  // 静态方法
  findById(id: any): Promise<T | undefined>;
  findByIds(ids: any[]): Promise<T[]>;
  create(data: Partial<T>): Promise<T>;
  update(id: any, data: Partial<T>): Promise<T>;
  delete(id: any): Promise<number>;
  
  // 关系查询
  withGraphFetched(expression: string): QueryBuilder<T>;
  withGraphJoined(expression: string): QueryBuilder<T>;
}

// 关系定义
export interface RelationMapping {
  relation: RelationType;
  modelClass: string | ModelClass;
  join: RelationJoin;
  modify?: string | ((query: QueryBuilder) => void);
  filter?: (query: QueryBuilder) => void;
}

export enum RelationType {
  BelongsToOneRelation = 'BelongsToOneRelation',
  HasOneRelation = 'HasOneRelation',
  HasManyRelation = 'HasManyRelation',
  ManyToManyRelation = 'ManyToManyRelation',
  HasOneThroughRelation = 'HasOneThroughRelation'
}

export interface RelationJoin {
  from: string | string[];
  to: string | string[];
  through?: RelationThrough;
}

export interface RelationThrough {
  from: string | string[];
  to: string | string[];
  modelClass?: string | ModelClass;
  extra?: string[];
}

// 验证规则
export interface ValidationRule {
  field: string;
  rules: ValidationConstraint[];
  message?: string;
}

export interface ValidationConstraint {
  type: ValidationType;
  value?: any;
  message?: string;
}

export enum ValidationType {
  REQUIRED = 'required',
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  DATE = 'date',
  EMAIL = 'email',
  URL = 'url',
  UUID = 'uuid',
  MIN = 'min',
  MAX = 'max',
  MIN_LENGTH = 'minLength',
  MAX_LENGTH = 'maxLength',
  PATTERN = 'pattern',
  IN = 'in',
  NOT_IN = 'notIn',
  UNIQUE = 'unique',
  EXISTS = 'exists'
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
  constraint?: string;
}

export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
}

// 数据库钩子
export interface ModelHooks<T = any> {
  beforeInsert?(model: T, context: HookContext): Promise<void> | void;
  afterInsert?(model: T, context: HookContext): Promise<void> | void;
  beforeUpdate?(model: T, context: HookContext): Promise<void> | void;
  afterUpdate?(model: T, context: HookContext): Promise<void> | void;
  beforeDelete?(model: T, context: HookContext): Promise<void> | void;
  afterDelete?(model: T, context: HookContext): Promise<void> | void;
  beforeFind?(context: HookContext): Promise<void> | void;
  afterFind?(models: T[], context: HookContext): Promise<void> | void;
}

export interface HookContext {
  transaction?: Transaction;
  user?: any;
  metadata?: Record<string, any>;
}

// 缓存策略
export interface CacheStrategy {
  enabled: boolean;
  ttl: number;
  keyPrefix?: string;
  tags?: string[];
  invalidateOn?: CacheInvalidationRule[];
}

export interface CacheInvalidationRule {
  event: 'insert' | 'update' | 'delete';
  tables?: string[];
  conditions?: Record<string, any>;
}

// 数据库监控
export interface DatabaseMetrics {
  connections: ConnectionMetrics;
  queries: QueryMetrics;
  performance: PerformanceMetrics;
  errors: ErrorMetrics;
}

export interface ConnectionMetrics {
  active: number;
  idle: number;
  total: number;
  waiting: number;
}

export interface QueryMetrics {
  total: number;
  successful: number;
  failed: number;
  averageTime: number;
  slowQueries: SlowQuery[];
}

export interface PerformanceMetrics {
  cpu: number;
  memory: number;
  diskIO: number;
  networkIO: number;
}

export interface ErrorMetrics {
  total: number;
  byType: Record<string, number>;
  recent: DatabaseError[];
}

export interface SlowQuery {
  sql: string;
  duration: number;
  timestamp: Date;
  parameters?: any[];
}

export interface DatabaseError {
  message: string;
  code?: string;
  sql?: string;
  parameters?: any[];
  timestamp: Date;
  stack?: string;
}

// 数据库备份
export interface BackupConfig {
  enabled: boolean;
  schedule: string; // cron表达式
  retention: number; // 保留天数
  compression: boolean;
  encryption?: BackupEncryption;
  storage: BackupStorage;
}

export interface BackupEncryption {
  enabled: boolean;
  algorithm: string;
  key: string;
}

export interface BackupStorage {
  type: 'local' | 's3' | 'gcs' | 'azure';
  config: Record<string, any>;
}

export interface BackupInfo {
  id: string;
  filename: string;
  size: number;
  createdAt: Date;
  status: 'pending' | 'running' | 'completed' | 'failed';
  error?: string;
}

// 数据同步
export interface SyncConfig {
  enabled: boolean;
  direction: 'push' | 'pull' | 'bidirectional';
  schedule?: string;
  tables: string[];
  filters?: Record<string, any>;
  conflictResolution: 'source' | 'target' | 'latest' | 'manual';
}

export interface SyncStatus {
  lastSync: Date;
  status: 'idle' | 'running' | 'error';
  recordsSynced: number;
  conflicts: SyncConflict[];
}

export interface SyncConflict {
  table: string;
  recordId: string;
  sourceData: any;
  targetData: any;
  timestamp: Date;
  resolved: boolean;
}

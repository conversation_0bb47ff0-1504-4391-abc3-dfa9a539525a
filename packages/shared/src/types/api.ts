// API相关类型定义

// HTTP方法
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

// API端点配置
export interface ApiEndpoint {
  method: HttpMethod;
  path: string;
  description?: string;
  auth?: boolean;
  permissions?: string[];
  rateLimit?: RateLimit;
  validation?: ValidationSchema;
}

// 限流配置
export interface RateLimit {
  windowMs: number;
  maxRequests: number;
  message?: string;
}

// 验证模式
export interface ValidationSchema {
  params?: any;
  query?: any;
  body?: any;
  headers?: any;
}

// API错误代码
export const API_ERROR_CODES = {
  // 认证错误 (1000-1099)
  UNAUTHORIZED: '1001',
  TOKEN_EXPIRED: '1002',
  TOKEN_INVALID: '1003',
  INSUFFICIENT_PERMISSIONS: '1004',
  ACCOUNT_LOCKED: '1005',
  ACCOUNT_SUSPENDED: '1006',
  
  // 验证错误 (1100-1199)
  VALIDATION_ERROR: '1101',
  MISSING_REQUIRED_FIELD: '1102',
  INVALID_FORMAT: '1103',
  INVALID_VALUE: '1104',
  DUPLICATE_VALUE: '1105',
  
  // 业务错误 (2000-2999)
  RESOURCE_NOT_FOUND: '2001',
  RESOURCE_ALREADY_EXISTS: '2002',
  RESOURCE_CONFLICT: '2003',
  OPERATION_NOT_ALLOWED: '2004',
  BUSINESS_RULE_VIOLATION: '2005',
  
  // 客户相关错误 (2100-2199)
  CUSTOMER_NOT_FOUND: '2101',
  CUSTOMER_ALREADY_EXISTS: '2102',
  CUSTOMER_INACTIVE: '2103',
  
  // 线索相关错误 (2200-2299)
  LEAD_NOT_FOUND: '2201',
  LEAD_ALREADY_CONVERTED: '2202',
  LEAD_ASSIGNMENT_FAILED: '2203',
  
  // 商机相关错误 (2300-2399)
  OPPORTUNITY_NOT_FOUND: '2301',
  OPPORTUNITY_CLOSED: '2302',
  INVALID_STAGE_TRANSITION: '2303',
  
  // 合同相关错误 (2400-2499)
  CONTRACT_NOT_FOUND: '2401',
  CONTRACT_ALREADY_SIGNED: '2402',
  CONTRACT_EXPIRED: '2403',
  
  // 项目相关错误 (2500-2599)
  PROJECT_NOT_FOUND: '2501',
  PROJECT_ALREADY_COMPLETED: '2502',
  TASK_DEPENDENCY_VIOLATION: '2503',
  
  // 供应链相关错误 (2600-2699)
  SUPPLIER_NOT_FOUND: '2601',
  BOM_NOT_FOUND: '2602',
  INSUFFICIENT_INVENTORY: '2603',
  
  // 服务相关错误 (2700-2799)
  TICKET_NOT_FOUND: '2701',
  TICKET_ALREADY_RESOLVED: '2702',
  SERVICE_UNAVAILABLE: '2703',
  
  // 财务相关错误 (2800-2899)
  INVOICE_NOT_FOUND: '2801',
  INVOICE_ALREADY_PAID: '2802',
  PAYMENT_FAILED: '2803',
  
  // 系统错误 (5000-5099)
  INTERNAL_SERVER_ERROR: '5001',
  DATABASE_ERROR: '5002',
  EXTERNAL_SERVICE_ERROR: '5003',
  CONFIGURATION_ERROR: '5004',
  
  // 网络错误 (5100-5199)
  NETWORK_ERROR: '5101',
  TIMEOUT_ERROR: '5102',
  CONNECTION_ERROR: '5103',
  
  // 文件错误 (5200-5299)
  FILE_NOT_FOUND: '5201',
  FILE_TOO_LARGE: '5202',
  INVALID_FILE_TYPE: '5203',
  FILE_UPLOAD_FAILED: '5204'
} as const;

// API错误类型
export type ApiErrorCode = typeof API_ERROR_CODES[keyof typeof API_ERROR_CODES];

// HTTP状态码映射
export const HTTP_STATUS_CODES = {
  OK: 200,
  CREATED: 201,
  ACCEPTED: 202,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504
} as const;

// 请求上下文
export interface RequestContext {
  requestId: string;
  userId?: string;
  userRole?: string;
  permissions?: string[];
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  correlationId?: string;
}

// API版本信息
export interface ApiVersion {
  version: string;
  deprecated?: boolean;
  deprecationDate?: Date;
  supportEndDate?: Date;
  changelog?: string;
}

// 健康检查响应
export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: Date;
  uptime: number;
  version: string;
  services: ServiceHealth[];
}

export interface ServiceHealth {
  name: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime?: number;
  lastCheck: Date;
  error?: string;
}

// 批量操作
export interface BatchRequest<T> {
  operations: BatchOperation<T>[];
  options?: BatchOptions;
}

export interface BatchOperation<T> {
  operation: 'create' | 'update' | 'delete';
  data: T;
  id?: string;
}

export interface BatchOptions {
  continueOnError?: boolean;
  validateOnly?: boolean;
  timeout?: number;
}

export interface BatchResponse<T> {
  success: boolean;
  results: BatchResult<T>[];
  summary: BatchSummary;
}

export interface BatchResult<T> {
  success: boolean;
  data?: T;
  error?: ApiError;
  operation: BatchOperation<T>;
}

export interface BatchSummary {
  total: number;
  successful: number;
  failed: number;
  skipped: number;
  duration: number;
}

// 搜索相关
export interface SearchRequest {
  query: string;
  filters?: SearchFilter[];
  sort?: SearchSort[];
  pagination?: PaginationParams;
  highlight?: boolean;
  facets?: string[];
}

export interface SearchFilter {
  field: string;
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'contains' | 'startsWith' | 'endsWith';
  value: any;
}

export interface SearchSort {
  field: string;
  order: 'asc' | 'desc';
}

export interface SearchResponse<T> {
  items: T[];
  total: number;
  facets?: SearchFacet[];
  suggestions?: string[];
  took: number;
}

export interface SearchFacet {
  field: string;
  values: SearchFacetValue[];
}

export interface SearchFacetValue {
  value: string;
  count: number;
}

// 导出相关
export interface ExportRequest {
  format: 'csv' | 'xlsx' | 'pdf' | 'json';
  filters?: Record<string, any>;
  fields?: string[];
  options?: ExportOptions;
}

export interface ExportOptions {
  filename?: string;
  includeHeaders?: boolean;
  dateFormat?: string;
  timezone?: string;
  locale?: string;
}

export interface ExportResponse {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  downloadUrl?: string;
  expiresAt?: Date;
  error?: string;
  progress?: number;
}

// 导入相关
export interface ImportRequest {
  fileUrl: string;
  format: 'csv' | 'xlsx' | 'json';
  mapping?: FieldMapping[];
  options?: ImportOptions;
}

export interface FieldMapping {
  sourceField: string;
  targetField: string;
  transform?: string;
  defaultValue?: any;
}

export interface ImportOptions {
  skipFirstRow?: boolean;
  validateOnly?: boolean;
  updateExisting?: boolean;
  batchSize?: number;
}

export interface ImportResponse {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress?: number;
  summary?: ImportSummary;
  errors?: ImportError[];
}

export interface ImportSummary {
  totalRows: number;
  processedRows: number;
  successfulRows: number;
  failedRows: number;
  skippedRows: number;
}

export interface ImportError {
  row: number;
  field?: string;
  message: string;
  value?: any;
}

// WebSocket相关
export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: Date;
  id?: string;
}

export interface WebSocketEvent {
  event: string;
  data: any;
  room?: string;
  userId?: string;
}

// 缓存相关
export interface CacheOptions {
  ttl?: number;
  tags?: string[];
  namespace?: string;
}

export interface CacheKey {
  key: string;
  namespace?: string;
  version?: string;
}

// 重新导入基础类型
import { PaginationParams } from './index';

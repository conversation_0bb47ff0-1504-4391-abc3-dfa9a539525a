// 基础类型定义
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: ApiError;
  meta?: ResponseMeta;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  stack?: string;
}

export interface ResponseMeta {
  total?: number;
  page?: number;
  limit?: number;
  hasNext?: boolean;
  hasPrev?: boolean;
}

// 分页参数
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 查询参数
export interface QueryParams extends PaginationParams {
  search?: string;
  filters?: Record<string, any>;
  include?: string[];
}

// 用户相关类型
export interface User extends BaseEntity {
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  phone?: string;
  status: UserStatus;
  role: UserRole;
  permissions: Permission[];
  metadata: Record<string, any>;
  lastLoginAt?: Date;
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING = 'pending'
}

export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  SALES = 'sales',
  SUPPORT = 'support',
  USER = 'user'
}

export interface Permission {
  resource: string;
  actions: string[];
}

// 客户相关类型
export interface Customer extends BaseEntity {
  name: string;
  type: CustomerType;
  industry?: string;
  size?: CompanySize;
  website?: string;
  description?: string;
  status: CustomerStatus;
  contactInfo: ContactInfo;
  address?: Address;
  tags: string[];
  metadata: Record<string, any>;
  assignedTo?: string; // 用户ID
}

export enum CustomerType {
  INDIVIDUAL = 'individual',
  COMPANY = 'company',
  GOVERNMENT = 'government',
  NON_PROFIT = 'non_profit'
}

export enum CompanySize {
  STARTUP = 'startup',
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
  ENTERPRISE = 'enterprise'
}

export enum CustomerStatus {
  PROSPECT = 'prospect',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  CHURNED = 'churned'
}

export interface ContactInfo {
  email?: string;
  phone?: string;
  mobile?: string;
  fax?: string;
  wechat?: string;
  qq?: string;
}

export interface Address {
  country: string;
  province: string;
  city: string;
  district?: string;
  street: string;
  postalCode?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

// 线索相关类型
export interface Lead extends BaseEntity {
  customerId?: string;
  source: LeadSource;
  title: string;
  description?: string;
  status: LeadStatus;
  priority: Priority;
  score: number;
  contactInfo: ContactInfo;
  assignedTo?: string;
  tags: string[];
  metadata: Record<string, any>;
  convertedAt?: Date;
  convertedTo?: string; // 商机ID
}

export enum LeadSource {
  WEBSITE = 'website',
  SOCIAL_MEDIA = 'social_media',
  EMAIL = 'email',
  PHONE = 'phone',
  REFERRAL = 'referral',
  ADVERTISEMENT = 'advertisement',
  EVENT = 'event',
  PARTNER = 'partner',
  OTHER = 'other'
}

export enum LeadStatus {
  NEW = 'new',
  CONTACTED = 'contacted',
  QUALIFIED = 'qualified',
  UNQUALIFIED = 'unqualified',
  CONVERTED = 'converted',
  LOST = 'lost'
}

export enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

// 商机相关类型
export interface Opportunity extends BaseEntity {
  customerId: string;
  leadId?: string;
  name: string;
  description?: string;
  stage: OpportunityStage;
  probability: number;
  amount: number;
  currency: string;
  expectedCloseDate: Date;
  actualCloseDate?: Date;
  assignedTo: string;
  tags: string[];
  metadata: Record<string, any>;
}

export enum OpportunityStage {
  PROSPECTING = 'prospecting',
  QUALIFICATION = 'qualification',
  PROPOSAL = 'proposal',
  NEGOTIATION = 'negotiation',
  CLOSED_WON = 'closed_won',
  CLOSED_LOST = 'closed_lost'
}

// 合同相关类型
export interface Contract extends BaseEntity {
  opportunityId?: string;
  customerId: string;
  contractNumber: string;
  title: string;
  description?: string;
  type: ContractType;
  status: ContractStatus;
  amount: number;
  currency: string;
  startDate: Date;
  endDate: Date;
  signedDate?: Date;
  terms: ContractTerm[];
  attachments: Attachment[];
  assignedTo: string;
  metadata: Record<string, any>;
}

export enum ContractType {
  SALES = 'sales',
  SERVICE = 'service',
  SUBSCRIPTION = 'subscription',
  LICENSE = 'license',
  PARTNERSHIP = 'partnership'
}

export enum ContractStatus {
  DRAFT = 'draft',
  PENDING_APPROVAL = 'pending_approval',
  APPROVED = 'approved',
  SENT = 'sent',
  SIGNED = 'signed',
  ACTIVE = 'active',
  EXPIRED = 'expired',
  TERMINATED = 'terminated'
}

export interface ContractTerm {
  id: string;
  title: string;
  content: string;
  type: 'payment' | 'delivery' | 'warranty' | 'liability' | 'other';
}

export interface Attachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  uploadedAt: Date;
  uploadedBy: string;
}

// 项目相关类型
export interface Project extends BaseEntity {
  contractId?: string;
  customerId: string;
  name: string;
  description?: string;
  status: ProjectStatus;
  priority: Priority;
  budget: number;
  currency: string;
  startDate: Date;
  endDate: Date;
  actualStartDate?: Date;
  actualEndDate?: Date;
  progress: number;
  managerId: string;
  teamMembers: string[];
  tags: string[];
  metadata: Record<string, any>;
}

export enum ProjectStatus {
  PLANNING = 'planning',
  IN_PROGRESS = 'in_progress',
  ON_HOLD = 'on_hold',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// 任务相关类型
export interface Task extends BaseEntity {
  projectId: string;
  parentTaskId?: string;
  name: string;
  description?: string;
  status: TaskStatus;
  priority: Priority;
  assignedTo?: string;
  estimatedHours: number;
  actualHours: number;
  startDate: Date;
  endDate: Date;
  dependencies: string[];
  tags: string[];
  metadata: Record<string, any>;
}

export enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  REVIEW = 'review',
  DONE = 'done',
  CANCELLED = 'cancelled'
}

// BOM相关类型
export interface BOM extends BaseEntity {
  projectId?: string;
  productId?: string;
  name: string;
  version: string;
  description?: string;
  status: BOMStatus;
  items: BOMItem[];
  totalCost: number;
  currency: string;
  createdBy: string;
  approvedBy?: string;
  approvedAt?: Date;
  metadata: Record<string, any>;
}

export enum BOMStatus {
  DRAFT = 'draft',
  PENDING_APPROVAL = 'pending_approval',
  APPROVED = 'approved',
  ACTIVE = 'active',
  OBSOLETE = 'obsolete'
}

export interface BOMItem {
  id: string;
  materialId: string;
  materialName: string;
  specification: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalPrice: number;
  supplier?: string;
  leadTime: number;
  notes?: string;
}

// 供应商相关类型
export interface Supplier extends BaseEntity {
  name: string;
  code: string;
  type: SupplierType;
  status: SupplierStatus;
  contactInfo: ContactInfo;
  address: Address;
  paymentTerms: string;
  deliveryTerms: string;
  qualityRating: number;
  priceRating: number;
  deliveryRating: number;
  tags: string[];
  metadata: Record<string, any>;
}

export enum SupplierType {
  MANUFACTURER = 'manufacturer',
  DISTRIBUTOR = 'distributor',
  WHOLESALER = 'wholesaler',
  SERVICE_PROVIDER = 'service_provider'
}

export enum SupplierStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BLACKLISTED = 'blacklisted',
  PENDING_APPROVAL = 'pending_approval'
}

// 采购相关类型
export interface PurchaseOrder extends BaseEntity {
  orderNumber: string;
  supplierId: string;
  projectId?: string;
  bomId?: string;
  status: PurchaseOrderStatus;
  items: PurchaseOrderItem[];
  totalAmount: number;
  currency: string;
  orderDate: Date;
  expectedDeliveryDate: Date;
  actualDeliveryDate?: Date;
  createdBy: string;
  approvedBy?: string;
  approvedAt?: Date;
  metadata: Record<string, any>;
}

export enum PurchaseOrderStatus {
  DRAFT = 'draft',
  PENDING_APPROVAL = 'pending_approval',
  APPROVED = 'approved',
  SENT = 'sent',
  CONFIRMED = 'confirmed',
  PARTIALLY_RECEIVED = 'partially_received',
  RECEIVED = 'received',
  CANCELLED = 'cancelled'
}

export interface PurchaseOrderItem {
  id: string;
  materialId: string;
  materialName: string;
  specification: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalPrice: number;
  receivedQuantity: number;
  notes?: string;
}

// 服务工单相关类型
export interface ServiceTicket extends BaseEntity {
  ticketNumber: string;
  customerId: string;
  projectId?: string;
  title: string;
  description: string;
  type: TicketType;
  priority: Priority;
  status: TicketStatus;
  category: string;
  assignedTo?: string;
  reportedBy: string;
  resolvedBy?: string;
  resolvedAt?: Date;
  resolution?: string;
  tags: string[];
  attachments: Attachment[];
  metadata: Record<string, any>;
}

export enum TicketType {
  INCIDENT = 'incident',
  REQUEST = 'request',
  CHANGE = 'change',
  PROBLEM = 'problem'
}

export enum TicketStatus {
  OPEN = 'open',
  IN_PROGRESS = 'in_progress',
  PENDING = 'pending',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
  CANCELLED = 'cancelled'
}

// 财务相关类型
export interface Invoice extends BaseEntity {
  invoiceNumber: string;
  contractId?: string;
  customerId: string;
  projectId?: string;
  type: InvoiceType;
  status: InvoiceStatus;
  items: InvoiceItem[];
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
  currency: string;
  issueDate: Date;
  dueDate: Date;
  paidDate?: Date;
  paidAmount: number;
  paymentMethod?: string;
  notes?: string;
  metadata: Record<string, any>;
}

export enum InvoiceType {
  SALES = 'sales',
  SERVICE = 'service',
  SUBSCRIPTION = 'subscription',
  EXPENSE = 'expense'
}

export enum InvoiceStatus {
  DRAFT = 'draft',
  SENT = 'sent',
  VIEWED = 'viewed',
  PARTIALLY_PAID = 'partially_paid',
  PAID = 'paid',
  OVERDUE = 'overdue',
  CANCELLED = 'cancelled'
}

export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalPrice: number;
  taxRate: number;
  taxAmount: number;
}

// 事件相关类型
export interface Event {
  id: string;
  type: string;
  source: string;
  data: any;
  timestamp: Date;
  version: string;
  correlationId?: string;
  causationId?: string;
}

// MCP协议相关类型
export interface MCPMessage {
  id: string;
  type: MCPMessageType;
  source: string;
  target: string;
  payload: any;
  timestamp: Date;
  correlationId?: string;
}

export enum MCPMessageType {
  REQUEST = 'request',
  RESPONSE = 'response',
  EVENT = 'event',
  COMMAND = 'command'
}

export interface MCPDevice {
  id: string;
  name: string;
  type: DeviceType;
  status: DeviceStatus;
  capabilities: string[];
  metadata: Record<string, any>;
  lastSeen: Date;
}

export enum DeviceType {
  SMART_SPEAKER = 'smart_speaker',
  SMART_DISPLAY = 'smart_display',
  MOBILE_APP = 'mobile_app',
  WEB_APP = 'web_app',
  IOT_DEVICE = 'iot_device'
}

export enum DeviceStatus {
  ONLINE = 'online',
  OFFLINE = 'offline',
  CONNECTING = 'connecting',
  ERROR = 'error'
}

// 配置相关类型
export interface SystemConfig {
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description?: string;
  category: string;
  isPublic: boolean;
  updatedBy: string;
  updatedAt: Date;
}

// 审计日志类型
export interface AuditLog extends BaseEntity {
  userId: string;
  action: string;
  resource: string;
  resourceId: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  metadata: Record<string, any>;
}

// 通知相关类型
export interface Notification extends BaseEntity {
  userId: string;
  type: NotificationType;
  title: string;
  content: string;
  data?: Record<string, any>;
  isRead: boolean;
  readAt?: Date;
  expiresAt?: Date;
}

export enum NotificationType {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  SUCCESS = 'success'
}

// 工作流相关类型
export interface WorkflowDefinition extends BaseEntity {
  name: string;
  version: string;
  description?: string;
  status: WorkflowStatus;
  definition: any; // BPMN或其他工作流定义
  variables: WorkflowVariable[];
  createdBy: string;
}

export enum WorkflowStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DEPRECATED = 'deprecated'
}

export interface WorkflowVariable {
  name: string;
  type: string;
  defaultValue?: any;
  required: boolean;
  description?: string;
}

export interface WorkflowInstance extends BaseEntity {
  definitionId: string;
  businessKey?: string;
  status: WorkflowInstanceStatus;
  variables: Record<string, any>;
  startedBy: string;
  endedAt?: Date;
  endedBy?: string;
}

export enum WorkflowInstanceStatus {
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  SUSPENDED = 'suspended'
}

// 导出所有类型
export * from './api';
export * from './auth';
export * from './database';

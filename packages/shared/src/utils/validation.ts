import Joi from 'joi';
import { ValidationError, ValidationResult } from '../types/database';

// 验证规则构建器
export class ValidationBuilder {
  private schema: Joi.ObjectSchema;

  constructor() {
    this.schema = Joi.object();
  }

  // 字符串验证
  string(field: string): StringValidator {
    return new StringValidator(this, field);
  }

  // 数字验证
  number(field: string): NumberValidator {
    return new NumberValidator(this, field);
  }

  // 布尔值验证
  boolean(field: string): BooleanValidator {
    return new BooleanValidator(this, field);
  }

  // 日期验证
  date(field: string): DateValidator {
    return new DateValidator(this, field);
  }

  // 数组验证
  array(field: string): ArrayValidator {
    return new ArrayValidator(this, field);
  }

  // 对象验证
  object(field: string): ObjectValidator {
    return new ObjectValidator(this, field);
  }

  // 添加字段规则
  addField(field: string, rule: Joi.Schema): ValidationBuilder {
    this.schema = this.schema.keys({ [field]: rule });
    return this;
  }

  // 构建最终模式
  build(): Joi.ObjectSchema {
    return this.schema;
  }

  // 验证数据
  validate(data: any): ValidationResult {
    const { error, value } = this.schema.validate(data, { 
      abortEarly: false,
      allowUnknown: false,
      stripUnknown: true
    });

    if (error) {
      const errors: ValidationError[] = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value,
        constraint: detail.type
      }));

      return {
        valid: false,
        errors
      };
    }

    return {
      valid: true,
      errors: []
    };
  }
}

// 基础验证器
abstract class BaseValidator<T extends BaseValidator<T>> {
  protected builder: ValidationBuilder;
  protected field: string;
  protected rule: Joi.Schema;

  constructor(builder: ValidationBuilder, field: string, rule: Joi.Schema) {
    this.builder = builder;
    this.field = field;
    this.rule = rule;
  }

  // 必填
  required(): T {
    this.rule = this.rule.required();
    return this as unknown as T;
  }

  // 可选
  optional(): T {
    this.rule = this.rule.optional();
    return this as unknown as T;
  }

  // 默认值
  default(value: any): T {
    this.rule = this.rule.default(value);
    return this as unknown as T;
  }

  // 自定义验证
  custom(fn: (value: any) => boolean | string, message?: string): T {
    this.rule = this.rule.custom((value, helpers) => {
      const result = fn(value);
      if (result === true) {
        return value;
      }
      return helpers.error('any.custom', { 
        message: typeof result === 'string' ? result : message 
      });
    });
    return this as unknown as T;
  }

  // 完成验证器配置
  done(): ValidationBuilder {
    return this.builder.addField(this.field, this.rule);
  }
}

// 字符串验证器
export class StringValidator extends BaseValidator<StringValidator> {
  constructor(builder: ValidationBuilder, field: string) {
    super(builder, field, Joi.string());
  }

  min(length: number): StringValidator {
    this.rule = this.rule.min(length);
    return this;
  }

  max(length: number): StringValidator {
    this.rule = this.rule.max(length);
    return this;
  }

  length(length: number): StringValidator {
    this.rule = this.rule.length(length);
    return this;
  }

  pattern(regex: RegExp, message?: string): StringValidator {
    this.rule = this.rule.pattern(regex, message);
    return this;
  }

  email(): StringValidator {
    this.rule = this.rule.email();
    return this;
  }

  uri(): StringValidator {
    this.rule = this.rule.uri();
    return this;
  }

  uuid(): StringValidator {
    this.rule = this.rule.uuid();
    return this;
  }

  alphanum(): StringValidator {
    this.rule = this.rule.alphanum();
    return this;
  }

  lowercase(): StringValidator {
    this.rule = this.rule.lowercase();
    return this;
  }

  uppercase(): StringValidator {
    this.rule = this.rule.uppercase();
    return this;
  }

  trim(): StringValidator {
    this.rule = this.rule.trim();
    return this;
  }

  valid(...values: string[]): StringValidator {
    this.rule = this.rule.valid(...values);
    return this;
  }

  invalid(...values: string[]): StringValidator {
    this.rule = this.rule.invalid(...values);
    return this;
  }
}

// 数字验证器
export class NumberValidator extends BaseValidator<NumberValidator> {
  constructor(builder: ValidationBuilder, field: string) {
    super(builder, field, Joi.number());
  }

  min(limit: number): NumberValidator {
    this.rule = this.rule.min(limit);
    return this;
  }

  max(limit: number): NumberValidator {
    this.rule = this.rule.max(limit);
    return this;
  }

  greater(limit: number): NumberValidator {
    this.rule = this.rule.greater(limit);
    return this;
  }

  less(limit: number): NumberValidator {
    this.rule = this.rule.less(limit);
    return this;
  }

  integer(): NumberValidator {
    this.rule = this.rule.integer();
    return this;
  }

  positive(): NumberValidator {
    this.rule = this.rule.positive();
    return this;
  }

  negative(): NumberValidator {
    this.rule = this.rule.negative();
    return this;
  }

  precision(limit: number): NumberValidator {
    this.rule = this.rule.precision(limit);
    return this;
  }

  multiple(base: number): NumberValidator {
    this.rule = this.rule.multiple(base);
    return this;
  }
}

// 布尔值验证器
export class BooleanValidator extends BaseValidator<BooleanValidator> {
  constructor(builder: ValidationBuilder, field: string) {
    super(builder, field, Joi.boolean());
  }

  truthy(...values: any[]): BooleanValidator {
    this.rule = this.rule.truthy(...values);
    return this;
  }

  falsy(...values: any[]): BooleanValidator {
    this.rule = this.rule.falsy(...values);
    return this;
  }
}

// 日期验证器
export class DateValidator extends BaseValidator<DateValidator> {
  constructor(builder: ValidationBuilder, field: string) {
    super(builder, field, Joi.date());
  }

  min(date: Date | string): DateValidator {
    this.rule = this.rule.min(date);
    return this;
  }

  max(date: Date | string): DateValidator {
    this.rule = this.rule.max(date);
    return this;
  }

  greater(date: Date | string): DateValidator {
    this.rule = this.rule.greater(date);
    return this;
  }

  less(date: Date | string): DateValidator {
    this.rule = this.rule.less(date);
    return this;
  }

  iso(): DateValidator {
    this.rule = this.rule.iso();
    return this;
  }
}

// 数组验证器
export class ArrayValidator extends BaseValidator<ArrayValidator> {
  constructor(builder: ValidationBuilder, field: string) {
    super(builder, field, Joi.array());
  }

  items(schema: Joi.Schema): ArrayValidator {
    this.rule = this.rule.items(schema);
    return this;
  }

  min(limit: number): ArrayValidator {
    this.rule = this.rule.min(limit);
    return this;
  }

  max(limit: number): ArrayValidator {
    this.rule = this.rule.max(limit);
    return this;
  }

  length(limit: number): ArrayValidator {
    this.rule = this.rule.length(limit);
    return this;
  }

  unique(comparator?: string | ((a: any, b: any) => boolean)): ArrayValidator {
    this.rule = this.rule.unique(comparator);
    return this;
  }
}

// 对象验证器
export class ObjectValidator extends BaseValidator<ObjectValidator> {
  constructor(builder: ValidationBuilder, field: string) {
    super(builder, field, Joi.object());
  }

  keys(schema: Record<string, Joi.Schema>): ObjectValidator {
    this.rule = this.rule.keys(schema);
    return this;
  }

  unknown(allow?: boolean): ObjectValidator {
    this.rule = this.rule.unknown(allow);
    return this;
  }

  min(limit: number): ObjectValidator {
    this.rule = this.rule.min(limit);
    return this;
  }

  max(limit: number): ObjectValidator {
    this.rule = this.rule.max(limit);
    return this;
  }

  length(limit: number): ObjectValidator {
    this.rule = this.rule.length(limit);
    return this;
  }
}

// 预定义验证模式
export const commonSchemas = {
  // 用户相关
  email: Joi.string().email().required(),
  password: Joi.string().min(8).max(128).pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/).required(),
  phone: Joi.string().pattern(/^[+]?[\d\s\-\(\)]+$/).min(10).max(20),
  name: Joi.string().min(1).max(100).trim(),
  
  // ID相关
  uuid: Joi.string().uuid().required(),
  objectId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/),
  
  // 分页相关
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  
  // 排序相关
  sortBy: Joi.string().max(50),
  sortOrder: Joi.string().valid('asc', 'desc').default('asc'),
  
  // 搜索相关
  search: Joi.string().max(200).trim(),
  
  // 日期相关
  dateRange: Joi.object({
    start: Joi.date().iso(),
    end: Joi.date().iso().greater(Joi.ref('start'))
  }),
  
  // 文件相关
  fileType: Joi.string().valid('jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx'),
  fileSize: Joi.number().max(10 * 1024 * 1024), // 10MB
  
  // 业务相关
  status: Joi.string().valid('active', 'inactive', 'pending', 'suspended'),
  priority: Joi.string().valid('low', 'medium', 'high', 'urgent'),
  currency: Joi.string().length(3).uppercase(),
  amount: Joi.number().precision(2).min(0),
  
  // 联系信息
  contactInfo: Joi.object({
    email: Joi.string().email(),
    phone: Joi.string().pattern(/^[+]?[\d\s\-\(\)]+$/),
    mobile: Joi.string().pattern(/^[+]?[\d\s\-\(\)]+$/),
    fax: Joi.string().pattern(/^[+]?[\d\s\-\(\)]+$/),
    wechat: Joi.string().max(50),
    qq: Joi.string().max(20)
  }),
  
  // 地址信息
  address: Joi.object({
    country: Joi.string().required(),
    province: Joi.string().required(),
    city: Joi.string().required(),
    district: Joi.string(),
    street: Joi.string().required(),
    postalCode: Joi.string().max(20),
    coordinates: Joi.object({
      latitude: Joi.number().min(-90).max(90),
      longitude: Joi.number().min(-180).max(180)
    })
  })
};

// 验证工具函数
export const validate = {
  // 验证单个值
  value: (value: any, schema: Joi.Schema): ValidationResult => {
    const { error } = schema.validate(value);
    if (error) {
      return {
        valid: false,
        errors: [{
          field: 'value',
          message: error.message,
          value,
          constraint: error.details[0]?.type
        }]
      };
    }
    return { valid: true, errors: [] };
  },

  // 验证对象
  object: (data: any, schema: Joi.ObjectSchema): ValidationResult => {
    const { error } = schema.validate(data, { abortEarly: false });
    if (error) {
      const errors: ValidationError[] = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value,
        constraint: detail.type
      }));
      return { valid: false, errors };
    }
    return { valid: true, errors: [] };
  },

  // 验证邮箱
  email: (email: string): boolean => {
    return commonSchemas.email.validate(email).error === undefined;
  },

  // 验证密码强度
  password: (password: string): boolean => {
    return commonSchemas.password.validate(password).error === undefined;
  },

  // 验证UUID
  uuid: (id: string): boolean => {
    return commonSchemas.uuid.validate(id).error === undefined;
  },

  // 验证手机号
  phone: (phone: string): boolean => {
    return commonSchemas.phone.validate(phone).error === undefined;
  }
};

// 创建验证中间件
export const createValidationMiddleware = (schema: Joi.ObjectSchema, target: 'body' | 'query' | 'params' = 'body') => {
  return (req: any, res: any, next: any) => {
    const data = req[target];
    const result = validate.object(data, schema);
    
    if (!result.valid) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Validation failed',
          details: result.errors
        }
      });
    }
    
    next();
  };
};

// 导出验证构建器实例
export const validator = new ValidationBuilder();

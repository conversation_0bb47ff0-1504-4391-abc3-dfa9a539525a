// 系统常量定义

// API相关常量
export const API = {
  VERSION: 'v1',
  PREFIX: '/api',
  TIMEOUT: 30000,
  MAX_REQUEST_SIZE: '10mb',
  RATE_LIMIT: {
    WINDOW_MS: 15 * 60 * 1000, // 15分钟
    MAX_REQUESTS: 100
  }
} as const;

// 数据库常量
export const DATABASE = {
  CONNECTION_TIMEOUT: 60000,
  QUERY_TIMEOUT: 30000,
  POOL_MIN: 2,
  POOL_MAX: 10,
  MIGRATION_TABLE: 'knex_migrations',
  SEED_TABLE: 'knex_migrations_lock'
} as const;

// 缓存常量
export const CACHE = {
  DEFAULT_TTL: 3600, // 1小时
  MAX_TTL: 86400, // 24小时
  KEY_PREFIX: 'linkagent:',
  NAMESPACES: {
    USER: 'user',
    SESSION: 'session',
    PERMISSION: 'permission',
    CONFIG: 'config',
    TEMP: 'temp'
  }
} as const;

// JWT常量
export const JWT = {
  ACCESS_TOKEN_EXPIRES: '24h',
  REFRESH_TOKEN_EXPIRES: '7d',
  ALGORITHM: 'HS256',
  ISSUER: 'linkagent',
  AUDIENCE: 'linkagent-users'
} as const;

// 文件上传常量
export const FILE_UPLOAD = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ],
  ALLOWED_EXTENSIONS: [
    '.jpg', '.jpeg', '.png', '.gif',
    '.pdf', '.doc', '.docx', '.xls', '.xlsx'
  ]
} as const;

// 分页常量
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 100,
  MIN_LIMIT: 1
} as const;

// 状态常量
export const STATUS = {
  USER: {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    SUSPENDED: 'suspended',
    PENDING: 'pending'
  },
  CUSTOMER: {
    PROSPECT: 'prospect',
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    CHURNED: 'churned'
  },
  LEAD: {
    NEW: 'new',
    CONTACTED: 'contacted',
    QUALIFIED: 'qualified',
    UNQUALIFIED: 'unqualified',
    CONVERTED: 'converted',
    LOST: 'lost'
  },
  OPPORTUNITY: {
    PROSPECTING: 'prospecting',
    QUALIFICATION: 'qualification',
    PROPOSAL: 'proposal',
    NEGOTIATION: 'negotiation',
    CLOSED_WON: 'closed_won',
    CLOSED_LOST: 'closed_lost'
  },
  CONTRACT: {
    DRAFT: 'draft',
    PENDING_APPROVAL: 'pending_approval',
    APPROVED: 'approved',
    SENT: 'sent',
    SIGNED: 'signed',
    ACTIVE: 'active',
    EXPIRED: 'expired',
    TERMINATED: 'terminated'
  },
  PROJECT: {
    PLANNING: 'planning',
    IN_PROGRESS: 'in_progress',
    ON_HOLD: 'on_hold',
    COMPLETED: 'completed',
    CANCELLED: 'cancelled'
  },
  TASK: {
    TODO: 'todo',
    IN_PROGRESS: 'in_progress',
    REVIEW: 'review',
    DONE: 'done',
    CANCELLED: 'cancelled'
  },
  TICKET: {
    OPEN: 'open',
    IN_PROGRESS: 'in_progress',
    PENDING: 'pending',
    RESOLVED: 'resolved',
    CLOSED: 'closed',
    CANCELLED: 'cancelled'
  },
  INVOICE: {
    DRAFT: 'draft',
    SENT: 'sent',
    VIEWED: 'viewed',
    PARTIALLY_PAID: 'partially_paid',
    PAID: 'paid',
    OVERDUE: 'overdue',
    CANCELLED: 'cancelled'
  }
} as const;

// 优先级常量
export const PRIORITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  URGENT: 'urgent'
} as const;

// 用户角色常量
export const ROLES = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  SALES: 'sales',
  SUPPORT: 'support',
  USER: 'user'
} as const;

// 权限常量
export const PERMISSIONS = {
  // 用户管理
  USER_CREATE: 'user:create',
  USER_READ: 'user:read',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',
  
  // 客户管理
  CUSTOMER_CREATE: 'customer:create',
  CUSTOMER_READ: 'customer:read',
  CUSTOMER_UPDATE: 'customer:update',
  CUSTOMER_DELETE: 'customer:delete',
  
  // 线索管理
  LEAD_CREATE: 'lead:create',
  LEAD_READ: 'lead:read',
  LEAD_UPDATE: 'lead:update',
  LEAD_DELETE: 'lead:delete',
  LEAD_ASSIGN: 'lead:assign',
  
  // 商机管理
  OPPORTUNITY_CREATE: 'opportunity:create',
  OPPORTUNITY_READ: 'opportunity:read',
  OPPORTUNITY_UPDATE: 'opportunity:update',
  OPPORTUNITY_DELETE: 'opportunity:delete',
  
  // 合同管理
  CONTRACT_CREATE: 'contract:create',
  CONTRACT_READ: 'contract:read',
  CONTRACT_UPDATE: 'contract:update',
  CONTRACT_DELETE: 'contract:delete',
  CONTRACT_APPROVE: 'contract:approve',
  CONTRACT_SIGN: 'contract:sign',
  
  // 项目管理
  PROJECT_CREATE: 'project:create',
  PROJECT_READ: 'project:read',
  PROJECT_UPDATE: 'project:update',
  PROJECT_DELETE: 'project:delete',
  PROJECT_MANAGE: 'project:manage',
  
  // 任务管理
  TASK_CREATE: 'task:create',
  TASK_READ: 'task:read',
  TASK_UPDATE: 'task:update',
  TASK_DELETE: 'task:delete',
  TASK_ASSIGN: 'task:assign',
  
  // 供应链管理
  BOM_CREATE: 'bom:create',
  BOM_READ: 'bom:read',
  BOM_UPDATE: 'bom:update',
  BOM_DELETE: 'bom:delete',
  BOM_APPROVE: 'bom:approve',
  
  SUPPLIER_CREATE: 'supplier:create',
  SUPPLIER_READ: 'supplier:read',
  SUPPLIER_UPDATE: 'supplier:update',
  SUPPLIER_DELETE: 'supplier:delete',
  
  PURCHASE_CREATE: 'purchase:create',
  PURCHASE_READ: 'purchase:read',
  PURCHASE_UPDATE: 'purchase:update',
  PURCHASE_DELETE: 'purchase:delete',
  PURCHASE_APPROVE: 'purchase:approve',
  
  // 服务管理
  TICKET_CREATE: 'ticket:create',
  TICKET_READ: 'ticket:read',
  TICKET_UPDATE: 'ticket:update',
  TICKET_DELETE: 'ticket:delete',
  TICKET_ASSIGN: 'ticket:assign',
  TICKET_RESOLVE: 'ticket:resolve',
  
  // 财务管理
  INVOICE_CREATE: 'invoice:create',
  INVOICE_READ: 'invoice:read',
  INVOICE_UPDATE: 'invoice:update',
  INVOICE_DELETE: 'invoice:delete',
  INVOICE_SEND: 'invoice:send',
  
  PAYMENT_CREATE: 'payment:create',
  PAYMENT_READ: 'payment:read',
  PAYMENT_UPDATE: 'payment:update',
  PAYMENT_DELETE: 'payment:delete',
  
  // 报表分析
  REPORT_READ: 'report:read',
  REPORT_CREATE: 'report:create',
  REPORT_EXPORT: 'report:export',
  
  // 系统管理
  SYSTEM_CONFIG: 'system:config',
  SYSTEM_MONITOR: 'system:monitor',
  SYSTEM_BACKUP: 'system:backup',
  
  // 审计日志
  AUDIT_READ: 'audit:read'
} as const;

// 事件类型常量
export const EVENTS = {
  // 用户事件
  USER_CREATED: 'user.created',
  USER_UPDATED: 'user.updated',
  USER_DELETED: 'user.deleted',
  USER_LOGIN: 'user.login',
  USER_LOGOUT: 'user.logout',
  
  // 客户事件
  CUSTOMER_CREATED: 'customer.created',
  CUSTOMER_UPDATED: 'customer.updated',
  CUSTOMER_DELETED: 'customer.deleted',
  
  // 线索事件
  LEAD_CREATED: 'lead.created',
  LEAD_UPDATED: 'lead.updated',
  LEAD_ASSIGNED: 'lead.assigned',
  LEAD_CONVERTED: 'lead.converted',
  
  // 商机事件
  OPPORTUNITY_CREATED: 'opportunity.created',
  OPPORTUNITY_UPDATED: 'opportunity.updated',
  OPPORTUNITY_STAGE_CHANGED: 'opportunity.stage_changed',
  OPPORTUNITY_WON: 'opportunity.won',
  OPPORTUNITY_LOST: 'opportunity.lost',
  
  // 合同事件
  CONTRACT_CREATED: 'contract.created',
  CONTRACT_UPDATED: 'contract.updated',
  CONTRACT_APPROVED: 'contract.approved',
  CONTRACT_SIGNED: 'contract.signed',
  CONTRACT_EXPIRED: 'contract.expired',
  
  // 项目事件
  PROJECT_CREATED: 'project.created',
  PROJECT_UPDATED: 'project.updated',
  PROJECT_STARTED: 'project.started',
  PROJECT_COMPLETED: 'project.completed',
  
  // 任务事件
  TASK_CREATED: 'task.created',
  TASK_UPDATED: 'task.updated',
  TASK_ASSIGNED: 'task.assigned',
  TASK_COMPLETED: 'task.completed',
  
  // 工单事件
  TICKET_CREATED: 'ticket.created',
  TICKET_UPDATED: 'ticket.updated',
  TICKET_ASSIGNED: 'ticket.assigned',
  TICKET_RESOLVED: 'ticket.resolved',
  
  // 发票事件
  INVOICE_CREATED: 'invoice.created',
  INVOICE_SENT: 'invoice.sent',
  INVOICE_PAID: 'invoice.paid',
  INVOICE_OVERDUE: 'invoice.overdue',
  
  // 系统事件
  SYSTEM_STARTED: 'system.started',
  SYSTEM_STOPPED: 'system.stopped',
  SYSTEM_ERROR: 'system.error'
} as const;

// 通知类型常量
export const NOTIFICATION_TYPES = {
  INFO: 'info',
  WARNING: 'warning',
  ERROR: 'error',
  SUCCESS: 'success'
} as const;

// 设备类型常量
export const DEVICE_TYPES = {
  SMART_SPEAKER: 'smart_speaker',
  SMART_DISPLAY: 'smart_display',
  MOBILE_APP: 'mobile_app',
  WEB_APP: 'web_app',
  IOT_DEVICE: 'iot_device'
} as const;

// MCP消息类型常量
export const MCP_MESSAGE_TYPES = {
  REQUEST: 'request',
  RESPONSE: 'response',
  EVENT: 'event',
  COMMAND: 'command'
} as const;

// 货币代码常量
export const CURRENCIES = {
  CNY: 'CNY', // 人民币
  USD: 'USD', // 美元
  EUR: 'EUR', // 欧元
  JPY: 'JPY', // 日元
  GBP: 'GBP', // 英镑
  HKD: 'HKD', // 港币
  TWD: 'TWD'  // 台币
} as const;

// 国家代码常量
export const COUNTRIES = {
  CN: 'CN', // 中国
  US: 'US', // 美国
  JP: 'JP', // 日本
  GB: 'GB', // 英国
  DE: 'DE', // 德国
  FR: 'FR', // 法国
  HK: 'HK', // 香港
  TW: 'TW'  // 台湾
} as const;

// 语言代码常量
export const LANGUAGES = {
  ZH_CN: 'zh-CN', // 简体中文
  ZH_TW: 'zh-TW', // 繁体中文
  EN_US: 'en-US', // 美式英语
  EN_GB: 'en-GB', // 英式英语
  JA_JP: 'ja-JP', // 日语
  DE_DE: 'de-DE', // 德语
  FR_FR: 'fr-FR'  // 法语
} as const;

// 时区常量
export const TIMEZONES = {
  ASIA_SHANGHAI: 'Asia/Shanghai',
  ASIA_TOKYO: 'Asia/Tokyo',
  ASIA_HONG_KONG: 'Asia/Hong_Kong',
  AMERICA_NEW_YORK: 'America/New_York',
  AMERICA_LOS_ANGELES: 'America/Los_Angeles',
  EUROPE_LONDON: 'Europe/London',
  EUROPE_PARIS: 'Europe/Paris',
  UTC: 'UTC'
} as const;

// 正则表达式常量
export const REGEX = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^[+]?[\d\s\-\(\)]+$/,
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  IPV4: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
  CHINESE: /[\u4e00-\u9fa5]/,
  ALPHANUMERIC: /^[a-zA-Z0-9]+$/,
  SLUG: /^[a-z0-9]+(?:-[a-z0-9]+)*$/
} as const;

// HTTP状态码常量
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  ACCEPTED: 202,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504
} as const;

// 环境常量
export const ENVIRONMENTS = {
  DEVELOPMENT: 'development',
  TESTING: 'testing',
  STAGING: 'staging',
  PRODUCTION: 'production'
} as const;

// 日志级别常量
export const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  HTTP: 'http',
  VERBOSE: 'verbose',
  DEBUG: 'debug',
  SILLY: 'silly'
} as const;

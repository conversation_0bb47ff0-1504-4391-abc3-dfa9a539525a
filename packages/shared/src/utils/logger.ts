import winston from 'winston';
import { format } from 'winston';

// 日志级别
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  HTTP = 'http',
  VERBOSE = 'verbose',
  DEBUG = 'debug',
  SILLY = 'silly'
}

// 日志配置接口
export interface LoggerConfig {
  level: LogLevel;
  format: 'json' | 'simple' | 'combined';
  transports: LogTransportConfig[];
  defaultMeta?: Record<string, any>;
}

export interface LogTransportConfig {
  type: 'console' | 'file' | 'http' | 'database';
  level?: LogLevel;
  config: Record<string, any>;
}

// 日志上下文
export interface LogContext {
  requestId?: string;
  userId?: string;
  correlationId?: string;
  service?: string;
  method?: string;
  url?: string;
  ip?: string;
  userAgent?: string;
  [key: string]: any;
}

// 创建默认日志器
export const createLogger = (config?: Partial<LoggerConfig>): winston.Logger => {
  const defaultConfig: LoggerConfig = {
    level: LogLevel.INFO,
    format: 'json',
    transports: [
      {
        type: 'console',
        config: {}
      }
    ],
    defaultMeta: {
      service: process.env.SERVICE_NAME || 'linkagent'
    }
  };

  const finalConfig = { ...defaultConfig, ...config };

  // 创建格式化器
  const logFormat = createLogFormat(finalConfig.format);

  // 创建传输器
  const transports = finalConfig.transports.map(createTransport);

  return winston.createLogger({
    level: finalConfig.level,
    format: logFormat,
    defaultMeta: finalConfig.defaultMeta,
    transports
  });
};

// 创建日志格式化器
const createLogFormat = (formatType: string) => {
  const baseFormat = format.combine(
    format.timestamp(),
    format.errors({ stack: true })
  );

  switch (formatType) {
    case 'json':
      return format.combine(
        baseFormat,
        format.json()
      );
    case 'simple':
      return format.combine(
        baseFormat,
        format.simple()
      );
    case 'combined':
      return format.combine(
        baseFormat,
        format.colorize(),
        format.printf(({ timestamp, level, message, service, ...meta }) => {
          const metaStr = Object.keys(meta).length ? JSON.stringify(meta) : '';
          return `${timestamp} [${service}] ${level}: ${message} ${metaStr}`;
        })
      );
    default:
      return format.combine(baseFormat, format.json());
  }
};

// 创建传输器
const createTransport = (config: LogTransportConfig): winston.transport => {
  switch (config.type) {
    case 'console':
      return new winston.transports.Console({
        level: config.level,
        ...config.config
      });
    case 'file':
      return new winston.transports.File({
        level: config.level,
        filename: config.config.filename || 'app.log',
        maxsize: config.config.maxsize || 5242880, // 5MB
        maxFiles: config.config.maxFiles || 5,
        ...config.config
      });
    case 'http':
      return new winston.transports.Http({
        level: config.level,
        host: config.config.host,
        port: config.config.port,
        path: config.config.path,
        ...config.config
      });
    default:
      throw new Error(`Unsupported transport type: ${config.type}`);
  }
};

// 默认日志器实例
export const logger = createLogger({
  level: (process.env.LOG_LEVEL as LogLevel) || LogLevel.INFO,
  format: (process.env.LOG_FORMAT as any) || 'json',
  transports: [
    {
      type: 'console',
      config: {
        handleExceptions: true,
        handleRejections: true
      }
    },
    ...(process.env.NODE_ENV === 'production' ? [
      {
        type: 'file' as const,
        level: LogLevel.ERROR,
        config: {
          filename: 'logs/error.log',
          handleExceptions: true
        }
      },
      {
        type: 'file' as const,
        config: {
          filename: 'logs/combined.log'
        }
      }
    ] : [])
  ]
});

// 结构化日志方法
export class StructuredLogger {
  private logger: winston.Logger;
  private context: LogContext;

  constructor(logger: winston.Logger, context: LogContext = {}) {
    this.logger = logger;
    this.context = context;
  }

  // 创建子日志器
  child(context: LogContext): StructuredLogger {
    return new StructuredLogger(this.logger, { ...this.context, ...context });
  }

  // 更新上下文
  setContext(context: LogContext): void {
    this.context = { ...this.context, ...context };
  }

  // 日志方法
  error(message: string, meta?: any): void {
    this.logger.error(message, { ...this.context, ...meta });
  }

  warn(message: string, meta?: any): void {
    this.logger.warn(message, { ...this.context, ...meta });
  }

  info(message: string, meta?: any): void {
    this.logger.info(message, { ...this.context, ...meta });
  }

  http(message: string, meta?: any): void {
    this.logger.http(message, { ...this.context, ...meta });
  }

  debug(message: string, meta?: any): void {
    this.logger.debug(message, { ...this.context, ...meta });
  }

  // 性能日志
  time(label: string): void {
    console.time(label);
  }

  timeEnd(label: string, meta?: any): void {
    console.timeEnd(label);
    this.info(`Timer: ${label}`, meta);
  }

  // 异步操作日志
  async logAsync<T>(
    operation: string,
    fn: () => Promise<T>,
    meta?: any
  ): Promise<T> {
    const start = Date.now();
    this.info(`Starting ${operation}`, meta);

    try {
      const result = await fn();
      const duration = Date.now() - start;
      this.info(`Completed ${operation}`, { ...meta, duration });
      return result;
    } catch (error) {
      const duration = Date.now() - start;
      this.error(`Failed ${operation}`, { 
        ...meta, 
        duration, 
        error: error instanceof Error ? error.message : error 
      });
      throw error;
    }
  }

  // 数据库查询日志
  logQuery(sql: string, params?: any[], duration?: number): void {
    this.debug('Database query', {
      sql: sql.replace(/\s+/g, ' ').trim(),
      params,
      duration
    });
  }

  // HTTP请求日志
  logRequest(req: any, res: any, duration: number): void {
    this.http('HTTP request', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });
  }

  // 错误日志
  logError(error: Error, context?: any): void {
    this.error(error.message, {
      stack: error.stack,
      name: error.name,
      ...context
    });
  }

  // 业务事件日志
  logEvent(event: string, data?: any): void {
    this.info(`Event: ${event}`, {
      event,
      data,
      timestamp: new Date().toISOString()
    });
  }

  // 安全事件日志
  logSecurityEvent(event: string, details: any): void {
    this.warn(`Security event: ${event}`, {
      event,
      ...details,
      timestamp: new Date().toISOString()
    });
  }

  // 审计日志
  logAudit(action: string, resource: string, details: any): void {
    this.info(`Audit: ${action}`, {
      action,
      resource,
      ...details,
      timestamp: new Date().toISOString()
    });
  }
}

// 创建结构化日志器实例
export const structuredLogger = new StructuredLogger(logger);

// 日志中间件工厂
export const createLoggerMiddleware = (logger: StructuredLogger) => {
  return (req: any, res: any, next: any) => {
    const start = Date.now();
    const requestId = req.headers['x-request-id'] || generateRequestId();
    
    // 设置请求上下文
    req.logger = logger.child({
      requestId,
      method: req.method,
      url: req.url,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    // 记录请求开始
    req.logger.http('Request started');

    // 监听响应结束
    res.on('finish', () => {
      const duration = Date.now() - start;
      req.logger.logRequest(req, res, duration);
    });

    next();
  };
};

// 生成请求ID
const generateRequestId = (): string => {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
};

// 日志采样器
export class LogSampler {
  private sampleRate: number;
  private counter: number = 0;

  constructor(sampleRate: number = 1.0) {
    this.sampleRate = Math.max(0, Math.min(1, sampleRate));
  }

  shouldLog(): boolean {
    this.counter++;
    return Math.random() < this.sampleRate;
  }

  setSampleRate(rate: number): void {
    this.sampleRate = Math.max(0, Math.min(1, rate));
  }
}

// 日志缓冲器
export class LogBuffer {
  private buffer: any[] = [];
  private maxSize: number;
  private flushInterval: number;
  private timer?: NodeJS.Timeout;

  constructor(maxSize: number = 100, flushInterval: number = 5000) {
    this.maxSize = maxSize;
    this.flushInterval = flushInterval;
    this.startTimer();
  }

  add(logEntry: any): void {
    this.buffer.push(logEntry);
    if (this.buffer.length >= this.maxSize) {
      this.flush();
    }
  }

  flush(): void {
    if (this.buffer.length > 0) {
      // 这里可以批量发送到日志服务
      console.log('Flushing log buffer:', this.buffer.length, 'entries');
      this.buffer = [];
    }
  }

  private startTimer(): void {
    this.timer = setInterval(() => {
      this.flush();
    }, this.flushInterval);
  }

  destroy(): void {
    if (this.timer) {
      clearInterval(this.timer);
    }
    this.flush();
  }
}

// 导出默认实例
export default structuredLogger;

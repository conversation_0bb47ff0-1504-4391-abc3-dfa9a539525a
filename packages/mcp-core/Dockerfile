# 多阶段构建 - 构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装构建依赖
RUN apk add --no-cache python3 make g++

# 复制package文件
COPY package*.json ./
COPY packages/shared/package*.json ./packages/shared/
COPY packages/mcp-core/package*.json ./packages/mcp-core/

# 安装依赖
RUN npm ci --only=production --ignore-scripts

# 复制源代码
COPY packages/shared/ ./packages/shared/
COPY packages/mcp-core/ ./packages/mcp-core/

# 构建共享包
WORKDIR /app/packages/shared
RUN npm run build

# 构建MCP核心
WORKDIR /app/packages/mcp-core
RUN npm run build

# 生产阶段
FROM node:18-alpine AS production

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S linkagent -u 1001

# 安装运行时依赖
RUN apk add --no-cache dumb-init curl

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/packages/shared/package*.json ./packages/shared/
COPY --from=builder /app/packages/mcp-core/package*.json ./packages/mcp-core/

# 复制构建产物
COPY --from=builder /app/packages/shared/dist/ ./packages/shared/dist/
COPY --from=builder /app/packages/mcp-core/dist/ ./packages/mcp-core/dist/
COPY --from=builder /app/node_modules/ ./node_modules/
COPY --from=builder /app/packages/shared/node_modules/ ./packages/shared/node_modules/
COPY --from=builder /app/packages/mcp-core/node_modules/ ./packages/mcp-core/node_modules/

# 创建日志目录
RUN mkdir -p /app/logs && \
    chown -R linkagent:nodejs /app

# 切换到非root用户
USER linkagent

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3000

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# 启动应用
CMD ["dumb-init", "node", "packages/mcp-core/dist/index.js"]

import { config as dotenvConfig } from 'dotenv';
import { z } from 'zod';

// 加载环境变量
dotenvConfig();

// 环境变量验证模式
const envSchema = z.object({
  // 基础配置
  NODE_ENV: z.enum(['development', 'testing', 'staging', 'production']).default('development'),
  PORT: z.string().transform(Number).default('3000'),
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'http', 'verbose', 'debug', 'silly']).default('info'),
  
  // 数据库配置
  DATABASE_URL: z.string(),
  DB_HOST: z.string().default('localhost'),
  DB_PORT: z.string().transform(Number).default('5432'),
  DB_NAME: z.string().default('linkagent'),
  DB_USER: z.string().default('admin'),
  DB_PASSWORD: z.string(),
  
  // Redis配置
  REDIS_URL: z.string().default('redis://localhost:6379'),
  REDIS_HOST: z.string().default('localhost'),
  REDIS_PORT: z.string().transform(Number).default('6379'),
  REDIS_PASSWORD: z.string().optional(),
  
  // MongoDB配置
  MONGODB_URL: z.string().default('mongodb://localhost:27017/linkagent'),
  
  // RabbitMQ配置
  RABBITMQ_URL: z.string().default('amqp://localhost:5672'),
  
  // Elasticsearch配置
  ELASTICSEARCH_URL: z.string().default('http://localhost:9200'),
  
  // JWT配置
  JWT_SECRET: z.string(),
  JWT_EXPIRES_IN: z.string().default('24h'),
  JWT_REFRESH_EXPIRES_IN: z.string().default('7d'),
  
  // 加密配置
  ENCRYPTION_KEY: z.string().optional(),
  HASH_SALT_ROUNDS: z.string().transform(Number).default('12'),
  
  // AI服务配置
  OPENAI_API_KEY: z.string().optional(),
  OPENAI_MODEL: z.string().default('gpt-4'),
  OPENAI_EMBEDDING_MODEL: z.string().default('text-embedding-ada-002'),
  
  // 向量数据库配置
  PINECONE_API_KEY: z.string().optional(),
  PINECONE_ENVIRONMENT: z.string().optional(),
  PINECONE_INDEX_NAME: z.string().optional(),
  
  // 语音识别配置
  AZURE_SPEECH_KEY: z.string().optional(),
  AZURE_SPEECH_REGION: z.string().optional(),
  
  // OCR配置
  AZURE_VISION_KEY: z.string().optional(),
  AZURE_VISION_ENDPOINT: z.string().optional(),
  
  // 邮件服务配置
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z.string().transform(Number).optional(),
  SMTP_USER: z.string().optional(),
  SMTP_PASSWORD: z.string().optional(),
  SMTP_FROM: z.string().optional(),
  
  // 短信服务配置
  TWILIO_ACCOUNT_SID: z.string().optional(),
  TWILIO_AUTH_TOKEN: z.string().optional(),
  TWILIO_PHONE_NUMBER: z.string().optional(),
  
  // 文件存储配置
  FILE_STORAGE_TYPE: z.enum(['local', 's3', 'minio']).default('local'),
  FILE_UPLOAD_MAX_SIZE: z.string().transform(Number).default('********'),
  
  // AWS S3配置
  AWS_ACCESS_KEY_ID: z.string().optional(),
  AWS_SECRET_ACCESS_KEY: z.string().optional(),
  AWS_REGION: z.string().optional(),
  AWS_S3_BUCKET: z.string().optional(),
  
  // 微信配置
  WECHAT_APP_ID: z.string().optional(),
  WECHAT_APP_SECRET: z.string().optional(),
  
  // 钉钉配置
  DINGTALK_APP_KEY: z.string().optional(),
  DINGTALK_APP_SECRET: z.string().optional(),
  
  // 企业微信配置
  WEWORK_CORP_ID: z.string().optional(),
  WEWORK_CORP_SECRET: z.string().optional(),
  
  // 监控配置
  SENTRY_DSN: z.string().optional(),
  NEW_RELIC_LICENSE_KEY: z.string().optional(),
  
  // CORS配置
  CORS_ORIGIN: z.string().default('http://localhost:3002'),
  CORS_CREDENTIALS: z.string().transform(Boolean).default('true'),
  
  // API配置
  API_VERSION: z.string().default('v1'),
  API_PREFIX: z.string().default('/api'),
  API_TIMEOUT: z.string().transform(Number).default('30000'),
  
  // 限流配置
  RATE_LIMIT_WINDOW: z.string().transform(Number).default('900000'),
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).default('100'),
  
  // 会话配置
  SESSION_SECRET: z.string().optional(),
  SESSION_MAX_AGE: z.string().transform(Number).default('86400000'),
  
  // 缓存配置
  CACHE_TTL: z.string().transform(Number).default('3600'),
  CACHE_MAX_SIZE: z.string().transform(Number).default('1000'),
  
  // 工作流配置
  WORKFLOW_ENGINE: z.enum(['camunda', 'zeebe']).default('camunda'),
  CAMUNDA_REST_URL: z.string().optional(),
  
  // MCP协议配置
  MCP_SERVER_PORT: z.string().transform(Number).default('8080'),
  MCP_CLIENT_TIMEOUT: z.string().transform(Number).default('30000'),
  MCP_MAX_CONNECTIONS: z.string().transform(Number).default('1000'),
  
  // 智能设备配置
  XIAOMI_APP_ID: z.string().optional(),
  XIAOMI_APP_SECRET: z.string().optional(),
  
  // 开发工具配置
  DEBUG: z.string().optional(),
  SWAGGER_ENABLED: z.string().transform(Boolean).default('true'),
  MOCK_DATA_ENABLED: z.string().transform(Boolean).default('false'),
  
  // 安全配置
  SECURITY_HEADERS_ENABLED: z.string().transform(Boolean).default('true'),
  CSRF_PROTECTION_ENABLED: z.string().transform(Boolean).default('true'),
  XSS_PROTECTION_ENABLED: z.string().transform(Boolean).default('true'),
  
  // 国际化配置
  DEFAULT_LOCALE: z.string().default('zh-CN'),
  SUPPORTED_LOCALES: z.string().default('zh-CN,en-US,ja-JP'),
  
  // 时区配置
  DEFAULT_TIMEZONE: z.string().default('Asia/Shanghai'),
  
  // 功能开关
  FEATURE_AI_ASSISTANT: z.string().transform(Boolean).default('true'),
  FEATURE_VOICE_INPUT: z.string().transform(Boolean).default('true'),
  FEATURE_OCR_RECOGNITION: z.string().transform(Boolean).default('true'),
  FEATURE_SMART_RECOMMENDATION: z.string().transform(Boolean).default('true'),
  FEATURE_REAL_TIME_COLLABORATION: z.string().transform(Boolean).default('true')
});

// 验证环境变量
const envVars = envSchema.parse(process.env);

// 配置对象
export const config = {
  // 环境配置
  env: envVars.NODE_ENV,
  port: envVars.PORT,
  logLevel: envVars.LOG_LEVEL,
  
  // 数据库配置
  database: {
    url: envVars.DATABASE_URL,
    host: envVars.DB_HOST,
    port: envVars.DB_PORT,
    name: envVars.DB_NAME,
    user: envVars.DB_USER,
    password: envVars.DB_PASSWORD,
    ssl: envVars.NODE_ENV === 'production',
    pool: {
      min: 2,
      max: 10,
      acquireTimeoutMillis: 60000,
      createTimeoutMillis: 30000,
      destroyTimeoutMillis: 5000,
      idleTimeoutMillis: 30000,
      reapIntervalMillis: 1000,
      createRetryIntervalMillis: 200
    }
  },
  
  // Redis配置
  redis: {
    url: envVars.REDIS_URL,
    host: envVars.REDIS_HOST,
    port: envVars.REDIS_PORT,
    password: envVars.REDIS_PASSWORD,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
    lazyConnect: true
  },
  
  // MongoDB配置
  mongodb: {
    url: envVars.MONGODB_URL
  },
  
  // RabbitMQ配置
  rabbitmq: {
    url: envVars.RABBITMQ_URL
  },
  
  // Elasticsearch配置
  elasticsearch: {
    url: envVars.ELASTICSEARCH_URL
  },
  
  // JWT配置
  jwt: {
    secret: envVars.JWT_SECRET,
    expiresIn: envVars.JWT_EXPIRES_IN,
    refreshExpiresIn: envVars.JWT_REFRESH_EXPIRES_IN,
    algorithm: 'HS256' as const,
    issuer: 'linkagent',
    audience: 'linkagent-users'
  },
  
  // 加密配置
  encryption: {
    key: envVars.ENCRYPTION_KEY,
    saltRounds: envVars.HASH_SALT_ROUNDS
  },
  
  // AI服务配置
  ai: {
    openai: {
      apiKey: envVars.OPENAI_API_KEY,
      model: envVars.OPENAI_MODEL,
      embeddingModel: envVars.OPENAI_EMBEDDING_MODEL,
      maxTokens: 4000,
      temperature: 0.7
    },
    pinecone: {
      apiKey: envVars.PINECONE_API_KEY,
      environment: envVars.PINECONE_ENVIRONMENT,
      indexName: envVars.PINECONE_INDEX_NAME
    },
    azure: {
      speech: {
        key: envVars.AZURE_SPEECH_KEY,
        region: envVars.AZURE_SPEECH_REGION
      },
      vision: {
        key: envVars.AZURE_VISION_KEY,
        endpoint: envVars.AZURE_VISION_ENDPOINT
      }
    }
  },
  
  // 邮件配置
  email: {
    smtp: {
      host: envVars.SMTP_HOST,
      port: envVars.SMTP_PORT,
      user: envVars.SMTP_USER,
      password: envVars.SMTP_PASSWORD,
      from: envVars.SMTP_FROM
    }
  },
  
  // 短信配置
  sms: {
    twilio: {
      accountSid: envVars.TWILIO_ACCOUNT_SID,
      authToken: envVars.TWILIO_AUTH_TOKEN,
      phoneNumber: envVars.TWILIO_PHONE_NUMBER
    }
  },
  
  // 文件存储配置
  fileStorage: {
    type: envVars.FILE_STORAGE_TYPE,
    maxSize: envVars.FILE_UPLOAD_MAX_SIZE,
    allowedTypes: [
      'image/jpeg', 'image/png', 'image/gif',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ],
    aws: {
      accessKeyId: envVars.AWS_ACCESS_KEY_ID,
      secretAccessKey: envVars.AWS_SECRET_ACCESS_KEY,
      region: envVars.AWS_REGION,
      bucket: envVars.AWS_S3_BUCKET
    }
  },
  
  // 第三方集成配置
  integrations: {
    wechat: {
      appId: envVars.WECHAT_APP_ID,
      appSecret: envVars.WECHAT_APP_SECRET
    },
    dingtalk: {
      appKey: envVars.DINGTALK_APP_KEY,
      appSecret: envVars.DINGTALK_APP_SECRET
    },
    wework: {
      corpId: envVars.WEWORK_CORP_ID,
      corpSecret: envVars.WEWORK_CORP_SECRET
    },
    xiaomi: {
      appId: envVars.XIAOMI_APP_ID,
      appSecret: envVars.XIAOMI_APP_SECRET
    }
  },
  
  // 监控配置
  monitoring: {
    sentry: {
      dsn: envVars.SENTRY_DSN
    },
    newRelic: {
      licenseKey: envVars.NEW_RELIC_LICENSE_KEY
    }
  },
  
  // CORS配置
  cors: {
    origin: envVars.CORS_ORIGIN.split(','),
    credentials: envVars.CORS_CREDENTIALS
  },
  
  // API配置
  api: {
    version: envVars.API_VERSION,
    prefix: envVars.API_PREFIX,
    timeout: envVars.API_TIMEOUT,
    maxRequestSize: '10mb'
  },
  
  // 限流配置
  rateLimit: {
    windowMs: envVars.RATE_LIMIT_WINDOW,
    maxRequests: envVars.RATE_LIMIT_MAX_REQUESTS
  },
  
  // 会话配置
  session: {
    secret: envVars.SESSION_SECRET,
    maxAge: envVars.SESSION_MAX_AGE
  },
  
  // 缓存配置
  cache: {
    ttl: envVars.CACHE_TTL,
    maxSize: envVars.CACHE_MAX_SIZE
  },
  
  // 工作流配置
  workflow: {
    engine: envVars.WORKFLOW_ENGINE,
    camunda: {
      restUrl: envVars.CAMUNDA_REST_URL
    }
  },
  
  // MCP协议配置
  mcp: {
    serverPort: envVars.MCP_SERVER_PORT,
    clientTimeout: envVars.MCP_CLIENT_TIMEOUT,
    maxConnections: envVars.MCP_MAX_CONNECTIONS
  },
  
  // 开发工具配置
  dev: {
    debug: envVars.DEBUG,
    swaggerEnabled: envVars.SWAGGER_ENABLED,
    mockDataEnabled: envVars.MOCK_DATA_ENABLED
  },
  
  // 安全配置
  security: {
    headersEnabled: envVars.SECURITY_HEADERS_ENABLED,
    csrfProtectionEnabled: envVars.CSRF_PROTECTION_ENABLED,
    xssProtectionEnabled: envVars.XSS_PROTECTION_ENABLED
  },
  
  // 国际化配置
  i18n: {
    defaultLocale: envVars.DEFAULT_LOCALE,
    supportedLocales: envVars.SUPPORTED_LOCALES.split(','),
    defaultTimezone: envVars.DEFAULT_TIMEZONE
  },
  
  // 功能开关
  features: {
    aiAssistant: envVars.FEATURE_AI_ASSISTANT,
    voiceInput: envVars.FEATURE_VOICE_INPUT,
    ocrRecognition: envVars.FEATURE_OCR_RECOGNITION,
    smartRecommendation: envVars.FEATURE_SMART_RECOMMENDATION,
    realTimeCollaboration: envVars.FEATURE_REAL_TIME_COLLABORATION
  }
};

export default config;

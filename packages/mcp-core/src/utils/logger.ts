import winston from 'winston';
import { config } from '../config';

// 创建日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, service, requestId, ...meta }) => {
    const logEntry = {
      timestamp,
      level,
      message,
      service: service || 'mcp-core',
      ...(requestId && { requestId }),
      ...meta
    };
    return JSON.stringify(logEntry);
  })
);

// 创建控制台格式（开发环境）
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, service, requestId, ...meta }) => {
    const metaStr = Object.keys(meta).length ? `\n${JSON.stringify(meta, null, 2)}` : '';
    const reqId = requestId ? `[${requestId}]` : '';
    return `${timestamp} ${level} [${service || 'mcp-core'}]${reqId}: ${message}${metaStr}`;
  })
);

// 创建传输器
const transports: winston.transport[] = [];

// 控制台传输器
if (config.env !== 'production') {
  transports.push(
    new winston.transports.Console({
      format: consoleFormat,
      level: config.logLevel
    })
  );
} else {
  transports.push(
    new winston.transports.Console({
      format: logFormat,
      level: config.logLevel
    })
  );
}

// 文件传输器（生产环境）
if (config.env === 'production') {
  // 错误日志文件
  transports.push(
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
      format: logFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      handleExceptions: true,
      handleRejections: true
    })
  );

  // 组合日志文件
  transports.push(
    new winston.transports.File({
      filename: 'logs/combined.log',
      format: logFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5
    })
  );

  // HTTP访问日志文件
  transports.push(
    new winston.transports.File({
      filename: 'logs/access.log',
      level: 'http',
      format: logFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5
    })
  );
}

// 创建日志器
export const logger = winston.createLogger({
  level: config.logLevel,
  format: logFormat,
  defaultMeta: {
    service: 'mcp-core',
    version: process.env.npm_package_version || '1.0.0',
    environment: config.env,
    hostname: require('os').hostname(),
    pid: process.pid
  },
  transports,
  exitOnError: false
});

// 结构化日志类
export class StructuredLogger {
  private context: Record<string, any>;

  constructor(context: Record<string, any> = {}) {
    this.context = context;
  }

  // 创建子日志器
  child(additionalContext: Record<string, any>): StructuredLogger {
    return new StructuredLogger({ ...this.context, ...additionalContext });
  }

  // 设置上下文
  setContext(context: Record<string, any>): void {
    this.context = { ...this.context, ...context };
  }

  // 清除上下文
  clearContext(): void {
    this.context = {};
  }

  // 日志方法
  error(message: string, meta?: any): void {
    logger.error(message, { ...this.context, ...meta });
  }

  warn(message: string, meta?: any): void {
    logger.warn(message, { ...this.context, ...meta });
  }

  info(message: string, meta?: any): void {
    logger.info(message, { ...this.context, ...meta });
  }

  http(message: string, meta?: any): void {
    logger.http(message, { ...this.context, ...meta });
  }

  verbose(message: string, meta?: any): void {
    logger.verbose(message, { ...this.context, ...meta });
  }

  debug(message: string, meta?: any): void {
    logger.debug(message, { ...this.context, ...meta });
  }

  silly(message: string, meta?: any): void {
    logger.silly(message, { ...this.context, ...meta });
  }

  // 性能计时
  time(label: string): void {
    console.time(label);
  }

  timeEnd(label: string, meta?: any): void {
    console.timeEnd(label);
    this.debug(`Timer: ${label}`, meta);
  }

  // 异步操作日志
  async logAsync<T>(
    operation: string,
    fn: () => Promise<T>,
    meta?: any
  ): Promise<T> {
    const start = Date.now();
    this.debug(`Starting ${operation}`, meta);

    try {
      const result = await fn();
      const duration = Date.now() - start;
      this.info(`Completed ${operation}`, { ...meta, duration });
      return result;
    } catch (error) {
      const duration = Date.now() - start;
      this.error(`Failed ${operation}`, {
        ...meta,
        duration,
        error: error instanceof Error ? {
          name: error.name,
          message: error.message,
          stack: error.stack
        } : error
      });
      throw error;
    }
  }

  // 数据库查询日志
  logQuery(sql: string, params?: any[], duration?: number): void {
    this.debug('Database query', {
      sql: sql.replace(/\s+/g, ' ').trim(),
      params,
      duration
    });
  }

  // HTTP请求日志
  logRequest(method: string, url: string, statusCode: number, duration: number, meta?: any): void {
    const level = statusCode >= 400 ? 'warn' : 'http';
    this[level]('HTTP request', {
      method,
      url,
      statusCode,
      duration,
      ...meta
    });
  }

  // 业务事件日志
  logEvent(event: string, data?: any): void {
    this.info(`Event: ${event}`, {
      event,
      data,
      timestamp: new Date().toISOString()
    });
  }

  // 安全事件日志
  logSecurityEvent(event: string, details: any): void {
    this.warn(`Security event: ${event}`, {
      event,
      ...details,
      timestamp: new Date().toISOString()
    });
  }

  // 审计日志
  logAudit(action: string, resource: string, details: any): void {
    this.info(`Audit: ${action}`, {
      action,
      resource,
      ...details,
      timestamp: new Date().toISOString()
    });
  }

  // 错误日志
  logError(error: Error, context?: any): void {
    this.error(error.message, {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      ...context
    });
  }

  // 性能监控日志
  logPerformance(operation: string, duration: number, meta?: any): void {
    const level = duration > 1000 ? 'warn' : 'debug';
    this[level](`Performance: ${operation}`, {
      operation,
      duration,
      ...meta
    });
  }

  // 外部服务调用日志
  logExternalCall(service: string, method: string, url: string, duration: number, statusCode?: number): void {
    const level = statusCode && statusCode >= 400 ? 'warn' : 'debug';
    this[level](`External call: ${service}`, {
      service,
      method,
      url,
      duration,
      statusCode
    });
  }

  // 缓存操作日志
  logCache(operation: 'hit' | 'miss' | 'set' | 'del', key: string, meta?: any): void {
    this.debug(`Cache ${operation}`, {
      operation,
      key,
      ...meta
    });
  }

  // 队列操作日志
  logQueue(operation: 'enqueue' | 'dequeue' | 'process' | 'complete' | 'failed', job: string, meta?: any): void {
    this.debug(`Queue ${operation}`, {
      operation,
      job,
      ...meta
    });
  }
}

// 创建默认结构化日志器实例
export const structuredLogger = new StructuredLogger();

// 日志采样器（用于高频日志）
export class LogSampler {
  private sampleRate: number;
  private counter: number = 0;

  constructor(sampleRate: number = 1.0) {
    this.sampleRate = Math.max(0, Math.min(1, sampleRate));
  }

  shouldLog(): boolean {
    this.counter++;
    return Math.random() < this.sampleRate;
  }

  setSampleRate(rate: number): void {
    this.sampleRate = Math.max(0, Math.min(1, rate));
  }

  getStats(): { counter: number; sampleRate: number } {
    return {
      counter: this.counter,
      sampleRate: this.sampleRate
    };
  }
}

// 日志缓冲器（用于批量发送）
export class LogBuffer {
  private buffer: any[] = [];
  private maxSize: number;
  private flushInterval: number;
  private timer?: NodeJS.Timeout;
  private flushCallback?: (logs: any[]) => Promise<void>;

  constructor(
    maxSize: number = 100,
    flushInterval: number = 5000,
    flushCallback?: (logs: any[]) => Promise<void>
  ) {
    this.maxSize = maxSize;
    this.flushInterval = flushInterval;
    this.flushCallback = flushCallback;
    this.startTimer();
  }

  add(logEntry: any): void {
    this.buffer.push({
      ...logEntry,
      timestamp: new Date().toISOString()
    });

    if (this.buffer.length >= this.maxSize) {
      this.flush();
    }
  }

  async flush(): Promise<void> {
    if (this.buffer.length === 0) return;

    const logs = [...this.buffer];
    this.buffer = [];

    try {
      if (this.flushCallback) {
        await this.flushCallback(logs);
      } else {
        // 默认行为：输出到控制台
        logger.debug('Flushing log buffer', { count: logs.length });
      }
    } catch (error) {
      logger.error('Failed to flush log buffer', { error, count: logs.length });
      // 将失败的日志重新加入缓冲区
      this.buffer.unshift(...logs);
    }
  }

  private startTimer(): void {
    this.timer = setInterval(() => {
      this.flush().catch(error => {
        logger.error('Log buffer flush timer error', { error });
      });
    }, this.flushInterval);
  }

  destroy(): void {
    if (this.timer) {
      clearInterval(this.timer);
    }
    this.flush().catch(error => {
      logger.error('Log buffer destroy flush error', { error });
    });
  }

  getStats(): { bufferSize: number; maxSize: number; flushInterval: number } {
    return {
      bufferSize: this.buffer.length,
      maxSize: this.maxSize,
      flushInterval: this.flushInterval
    };
  }
}

// 导出默认实例
export default logger;

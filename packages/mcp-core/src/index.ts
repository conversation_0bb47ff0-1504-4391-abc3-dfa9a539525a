import 'express-async-errors';
import express from 'express';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';

import { config } from './config';
import { logger, structuredLogger } from './utils/logger';
import { errorHandler } from './middleware/error-handler';
import { authMiddleware } from './middleware/auth';
import { requestLogger } from './middleware/request-logger';
import { metricsMiddleware } from './middleware/metrics';
import { tracingMiddleware } from './middleware/tracing';
import { setupSwagger } from './utils/swagger';
import { setupDatabase } from './database';
import { setupRedis } from './utils/redis';
import { setupQueues } from './utils/queue';
import { setupWebSocket } from './websocket';
import { setupCron } from './utils/cron';
import { healthCheck } from './utils/health-check';

// 导入路由
import authRoutes from './routes/auth';
import userRoutes from './routes/users';
import customerRoutes from './routes/customers';
import leadRoutes from './routes/leads';
import opportunityRoutes from './routes/opportunities';
import contractRoutes from './routes/contracts';
import projectRoutes from './routes/projects';
import taskRoutes from './routes/tasks';
import bomRoutes from './routes/bom';
import supplierRoutes from './routes/suppliers';
import purchaseRoutes from './routes/purchase';
import ticketRoutes from './routes/tickets';
import invoiceRoutes from './routes/invoices';
import reportRoutes from './routes/reports';
import configRoutes from './routes/config';
import webhookRoutes from './routes/webhooks';
import mcpRoutes from './routes/mcp';

class Application {
  public app: express.Application;
  public server: any;
  public io: SocketIOServer;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: config.cors.origin,
        credentials: true
      }
    });
  }

  // 初始化应用
  public async initialize(): Promise<void> {
    try {
      logger.info('Initializing Link Agent MCP Core...');

      // 设置基础中间件
      this.setupMiddleware();

      // 设置数据库连接
      await setupDatabase();
      logger.info('Database connected successfully');

      // 设置Redis连接
      await setupRedis();
      logger.info('Redis connected successfully');

      // 设置消息队列
      await setupQueues();
      logger.info('Message queues initialized');

      // 设置WebSocket
      setupWebSocket(this.io);
      logger.info('WebSocket server initialized');

      // 设置定时任务
      setupCron();
      logger.info('Cron jobs initialized');

      // 设置路由
      this.setupRoutes();

      // 设置错误处理
      this.setupErrorHandling();

      // 设置API文档
      setupSwagger(this.app);

      logger.info('Link Agent MCP Core initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize application:', error);
      process.exit(1);
    }
  }

  // 设置中间件
  private setupMiddleware(): void {
    // 安全中间件
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // CORS中间件
    this.app.use(cors({
      origin: config.cors.origin,
      credentials: config.cors.credentials,
      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID']
    }));

    // 压缩中间件
    this.app.use(compression());

    // 请求体解析
    this.app.use(express.json({ limit: config.api.maxRequestSize }));
    this.app.use(express.urlencoded({ extended: true, limit: config.api.maxRequestSize }));

    // 限流中间件
    const limiter = rateLimit({
      windowMs: config.rateLimit.windowMs,
      max: config.rateLimit.maxRequests,
      message: {
        success: false,
        error: {
          code: 'TOO_MANY_REQUESTS',
          message: 'Too many requests from this IP'
        }
      },
      standardHeaders: true,
      legacyHeaders: false
    });
    this.app.use('/api', limiter);

    // 日志中间件
    if (config.env !== 'test') {
      this.app.use(morgan('combined', {
        stream: { write: (message) => logger.info(message.trim()) }
      }));
    }

    // 自定义中间件
    this.app.use(requestLogger);
    this.app.use(metricsMiddleware);
    this.app.use(tracingMiddleware);
  }

  // 设置路由
  private setupRoutes(): void {
    const apiPrefix = `/api/${config.api.version}`;

    // 健康检查
    this.app.get('/health', healthCheck);
    this.app.get('/ready', healthCheck);

    // API路由
    this.app.use(`${apiPrefix}/auth`, authRoutes);
    this.app.use(`${apiPrefix}/users`, authMiddleware, userRoutes);
    this.app.use(`${apiPrefix}/customers`, authMiddleware, customerRoutes);
    this.app.use(`${apiPrefix}/leads`, authMiddleware, leadRoutes);
    this.app.use(`${apiPrefix}/opportunities`, authMiddleware, opportunityRoutes);
    this.app.use(`${apiPrefix}/contracts`, authMiddleware, contractRoutes);
    this.app.use(`${apiPrefix}/projects`, authMiddleware, projectRoutes);
    this.app.use(`${apiPrefix}/tasks`, authMiddleware, taskRoutes);
    this.app.use(`${apiPrefix}/bom`, authMiddleware, bomRoutes);
    this.app.use(`${apiPrefix}/suppliers`, authMiddleware, supplierRoutes);
    this.app.use(`${apiPrefix}/purchase`, authMiddleware, purchaseRoutes);
    this.app.use(`${apiPrefix}/tickets`, authMiddleware, ticketRoutes);
    this.app.use(`${apiPrefix}/invoices`, authMiddleware, invoiceRoutes);
    this.app.use(`${apiPrefix}/reports`, authMiddleware, reportRoutes);
    this.app.use(`${apiPrefix}/config`, authMiddleware, configRoutes);
    this.app.use(`${apiPrefix}/webhooks`, webhookRoutes);
    this.app.use(`${apiPrefix}/mcp`, mcpRoutes);

    // 404处理
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: `Route ${req.method} ${req.originalUrl} not found`
        }
      });
    });
  }

  // 设置错误处理
  private setupErrorHandling(): void {
    this.app.use(errorHandler);

    // 未捕获异常处理
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      this.gracefulShutdown('SIGTERM');
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      this.gracefulShutdown('SIGTERM');
    });

    // 优雅关闭
    process.on('SIGTERM', () => this.gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => this.gracefulShutdown('SIGINT'));
  }

  // 启动服务器
  public async start(): Promise<void> {
    await this.initialize();

    this.server.listen(config.port, () => {
      logger.info(`🚀 Link Agent MCP Core server started on port ${config.port}`);
      logger.info(`📚 API Documentation: http://localhost:${config.port}/api-docs`);
      logger.info(`🏥 Health Check: http://localhost:${config.port}/health`);
      logger.info(`🌍 Environment: ${config.env}`);
    });
  }

  // 优雅关闭
  private async gracefulShutdown(signal: string): Promise<void> {
    logger.info(`Received ${signal}. Starting graceful shutdown...`);

    // 停止接受新连接
    this.server.close(async () => {
      logger.info('HTTP server closed');

      try {
        // 关闭数据库连接
        // await database.destroy();
        logger.info('Database connections closed');

        // 关闭Redis连接
        // await redis.disconnect();
        logger.info('Redis connections closed');

        // 关闭消息队列
        // await queue.close();
        logger.info('Message queues closed');

        logger.info('Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('Error during graceful shutdown:', error);
        process.exit(1);
      }
    });

    // 强制关闭超时
    setTimeout(() => {
      logger.error('Graceful shutdown timeout, forcing exit');
      process.exit(1);
    }, 30000);
  }
}

// 启动应用
const app = new Application();

if (require.main === module) {
  app.start().catch((error) => {
    logger.error('Failed to start application:', error);
    process.exit(1);
  });
}

export default app;

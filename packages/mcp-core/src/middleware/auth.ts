import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '../config';
import { logger } from '../utils/logger';
import { UserService } from '../services/user.service';
import { PermissionService } from '../services/permission.service';
import { JwtPayload, User, Permission } from '@linkagent/shared/types';
import { API_ERROR_CODES, HTTP_STATUS } from '@linkagent/shared/utils/constants';

// 扩展Request接口
declare global {
  namespace Express {
    interface Request {
      user?: User;
      permissions?: Permission[];
      requestId?: string;
    }
  }
}

// JWT验证中间件
export const authMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractToken(req);
    
    if (!token) {
      res.status(HTTP_STATUS.UNAUTHORIZED).json({
        success: false,
        error: {
          code: API_ERROR_CODES.UNAUTHORIZED,
          message: 'Access token is required'
        }
      });
      return;
    }

    // 验证JWT token
    const decoded = jwt.verify(token, config.jwt.secret) as JwtPayload;
    
    // 获取用户信息
    const userService = new UserService();
    const user = await userService.findById(decoded.sub);
    
    if (!user) {
      res.status(HTTP_STATUS.UNAUTHORIZED).json({
        success: false,
        error: {
          code: API_ERROR_CODES.UNAUTHORIZED,
          message: 'User not found'
        }
      });
      return;
    }

    // 检查用户状态
    if (user.status !== 'active') {
      res.status(HTTP_STATUS.UNAUTHORIZED).json({
        success: false,
        error: {
          code: API_ERROR_CODES.ACCOUNT_SUSPENDED,
          message: 'Account is not active'
        }
      });
      return;
    }

    // 获取用户权限
    const permissionService = new PermissionService();
    const permissions = await permissionService.getUserPermissions(user.id);

    // 设置请求上下文
    req.user = user;
    req.permissions = permissions;

    // 记录认证日志
    logger.debug('User authenticated', {
      userId: user.id,
      email: user.email,
      role: user.role,
      requestId: req.requestId
    });

    next();
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      res.status(HTTP_STATUS.UNAUTHORIZED).json({
        success: false,
        error: {
          code: API_ERROR_CODES.TOKEN_EXPIRED,
          message: 'Access token has expired'
        }
      });
      return;
    }

    if (error instanceof jwt.JsonWebTokenError) {
      res.status(HTTP_STATUS.UNAUTHORIZED).json({
        success: false,
        error: {
          code: API_ERROR_CODES.TOKEN_INVALID,
          message: 'Invalid access token'
        }
      });
      return;
    }

    logger.error('Authentication error:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: {
        code: API_ERROR_CODES.INTERNAL_SERVER_ERROR,
        message: 'Authentication failed'
      }
    });
  }
};

// 可选认证中间件（不强制要求认证）
export const optionalAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractToken(req);
    
    if (token) {
      const decoded = jwt.verify(token, config.jwt.secret) as JwtPayload;
      const userService = new UserService();
      const user = await userService.findById(decoded.sub);
      
      if (user && user.status === 'active') {
        const permissionService = new PermissionService();
        const permissions = await permissionService.getUserPermissions(user.id);
        
        req.user = user;
        req.permissions = permissions;
      }
    }
    
    next();
  } catch (error) {
    // 可选认证失败时不阻止请求继续
    logger.debug('Optional authentication failed:', error);
    next();
  }
};

// 权限检查中间件工厂
export const requirePermission = (resource: string, action: string) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user || !req.permissions) {
        res.status(HTTP_STATUS.UNAUTHORIZED).json({
          success: false,
          error: {
            code: API_ERROR_CODES.UNAUTHORIZED,
            message: 'Authentication required'
          }
        });
        return;
      }

      const permissionService = new PermissionService();
      const hasPermission = await permissionService.checkPermission(
        req.user.id,
        resource,
        action,
        req.body // 传递请求体作为上下文
      );

      if (!hasPermission) {
        res.status(HTTP_STATUS.FORBIDDEN).json({
          success: false,
          error: {
            code: API_ERROR_CODES.INSUFFICIENT_PERMISSIONS,
            message: `Insufficient permissions for ${action} on ${resource}`
          }
        });
        return;
      }

      logger.debug('Permission check passed', {
        userId: req.user.id,
        resource,
        action,
        requestId: req.requestId
      });

      next();
    } catch (error) {
      logger.error('Permission check error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: API_ERROR_CODES.INTERNAL_SERVER_ERROR,
          message: 'Permission check failed'
        }
      });
    }
  };
};

// 角色检查中间件工厂
export const requireRole = (roles: string | string[]) => {
  const allowedRoles = Array.isArray(roles) ? roles : [roles];
  
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(HTTP_STATUS.UNAUTHORIZED).json({
        success: false,
        error: {
          code: API_ERROR_CODES.UNAUTHORIZED,
          message: 'Authentication required'
        }
      });
      return;
    }

    if (!allowedRoles.includes(req.user.role)) {
      res.status(HTTP_STATUS.FORBIDDEN).json({
        success: false,
        error: {
          code: API_ERROR_CODES.INSUFFICIENT_PERMISSIONS,
          message: `Required role: ${allowedRoles.join(' or ')}`
        }
      });
      return;
    }

    logger.debug('Role check passed', {
      userId: req.user.id,
      userRole: req.user.role,
      requiredRoles: allowedRoles,
      requestId: req.requestId
    });

    next();
  };
};

// 管理员权限检查中间件
export const requireAdmin = requireRole('admin');

// 管理员或经理权限检查中间件
export const requireManagerOrAdmin = requireRole(['admin', 'manager']);

// 资源所有者检查中间件工厂
export const requireOwnership = (resourceIdParam: string = 'id') => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(HTTP_STATUS.UNAUTHORIZED).json({
          success: false,
          error: {
            code: API_ERROR_CODES.UNAUTHORIZED,
            message: 'Authentication required'
          }
        });
        return;
      }

      const resourceId = req.params[resourceIdParam];
      const userId = req.user.id;

      // 管理员可以访问所有资源
      if (req.user.role === 'admin') {
        next();
        return;
      }

      // 检查资源所有权
      const permissionService = new PermissionService();
      const isOwner = await permissionService.checkOwnership(userId, resourceId);

      if (!isOwner) {
        res.status(HTTP_STATUS.FORBIDDEN).json({
          success: false,
          error: {
            code: API_ERROR_CODES.INSUFFICIENT_PERMISSIONS,
            message: 'Access denied: not resource owner'
          }
        });
        return;
      }

      logger.debug('Ownership check passed', {
        userId,
        resourceId,
        requestId: req.requestId
      });

      next();
    } catch (error) {
      logger.error('Ownership check error:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: {
          code: API_ERROR_CODES.INTERNAL_SERVER_ERROR,
          message: 'Ownership check failed'
        }
      });
    }
  };
};

// API密钥认证中间件
export const apiKeyAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const apiKey = req.headers['x-api-key'] as string;
    
    if (!apiKey) {
      res.status(HTTP_STATUS.UNAUTHORIZED).json({
        success: false,
        error: {
          code: API_ERROR_CODES.UNAUTHORIZED,
          message: 'API key is required'
        }
      });
      return;
    }

    // 验证API密钥
    const userService = new UserService();
    const apiKeyInfo = await userService.validateApiKey(apiKey);
    
    if (!apiKeyInfo) {
      res.status(HTTP_STATUS.UNAUTHORIZED).json({
        success: false,
        error: {
          code: API_ERROR_CODES.UNAUTHORIZED,
          message: 'Invalid API key'
        }
      });
      return;
    }

    // 检查API密钥是否过期
    if (apiKeyInfo.expiresAt && new Date() > apiKeyInfo.expiresAt) {
      res.status(HTTP_STATUS.UNAUTHORIZED).json({
        success: false,
        error: {
          code: API_ERROR_CODES.TOKEN_EXPIRED,
          message: 'API key has expired'
        }
      });
      return;
    }

    // 获取API密钥关联的用户
    const user = await userService.findById(apiKeyInfo.userId);
    if (!user || user.status !== 'active') {
      res.status(HTTP_STATUS.UNAUTHORIZED).json({
        success: false,
        error: {
          code: API_ERROR_CODES.UNAUTHORIZED,
          message: 'Associated user not found or inactive'
        }
      });
      return;
    }

    // 设置请求上下文
    req.user = user;
    req.permissions = apiKeyInfo.permissions.map(p => ({ resource: p, actions: ['read', 'write'] }));

    // 更新API密钥最后使用时间
    await userService.updateApiKeyLastUsed(apiKeyInfo.id);

    logger.debug('API key authenticated', {
      apiKeyId: apiKeyInfo.id,
      userId: user.id,
      requestId: req.requestId
    });

    next();
  } catch (error) {
    logger.error('API key authentication error:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: {
        code: API_ERROR_CODES.INTERNAL_SERVER_ERROR,
        message: 'API key authentication failed'
      }
    });
  }
};

// 提取token的辅助函数
const extractToken = (req: Request): string | null => {
  const authHeader = req.headers.authorization;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  // 也可以从查询参数中获取token（用于WebSocket等场景）
  const tokenFromQuery = req.query.token as string;
  if (tokenFromQuery) {
    return tokenFromQuery;
  }
  
  return null;
};

// 生成JWT token
export const generateToken = (payload: Omit<JwtPayload, 'iat' | 'exp'>): string => {
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.expiresIn,
    issuer: config.jwt.issuer,
    audience: config.jwt.audience
  });
};

// 生成刷新token
export const generateRefreshToken = (userId: string, tokenId: string): string => {
  return jwt.sign(
    { sub: userId, tokenId },
    config.jwt.secret,
    {
      expiresIn: config.jwt.refreshExpiresIn,
      issuer: config.jwt.issuer,
      audience: config.jwt.audience
    }
  );
};

// 验证刷新token
export const verifyRefreshToken = (token: string): { userId: string; tokenId: string } => {
  const decoded = jwt.verify(token, config.jwt.secret) as any;
  return {
    userId: decoded.sub,
    tokenId: decoded.tokenId
  };
};

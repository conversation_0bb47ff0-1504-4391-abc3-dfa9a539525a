import { Request, Response, NextFunction } from 'express';
import { ValidationError } from 'joi';
import { logger } from '../utils/logger';
import { config } from '../config';
import { API_ERROR_CODES, HTTP_STATUS } from '@linkagent/shared/utils/constants';

// 自定义错误类
export class AppError extends Error {
  public statusCode: number;
  public code: string;
  public isOperational: boolean;
  public details?: any;

  constructor(
    message: string,
    statusCode: number = HTTP_STATUS.INTERNAL_SERVER_ERROR,
    code: string = API_ERROR_CODES.INTERNAL_SERVER_ERROR,
    isOperational: boolean = true,
    details?: any
  ) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = isOperational;
    this.details = details;

    Error.captureStackTrace(this, this.constructor);
  }
}

// 业务错误类
export class BusinessError extends AppError {
  constructor(message: string, code: string, details?: any) {
    super(message, HTTP_STATUS.BAD_REQUEST, code, true, details);
  }
}

// 验证错误类
export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, HTTP_STATUS.UNPROCESSABLE_ENTITY, API_ERROR_CODES.VALIDATION_ERROR, true, details);
  }
}

// 认证错误类
export class AuthenticationError extends AppError {
  constructor(message: string, code: string = API_ERROR_CODES.UNAUTHORIZED) {
    super(message, HTTP_STATUS.UNAUTHORIZED, code, true);
  }
}

// 授权错误类
export class AuthorizationError extends AppError {
  constructor(message: string, code: string = API_ERROR_CODES.INSUFFICIENT_PERMISSIONS) {
    super(message, HTTP_STATUS.FORBIDDEN, code, true);
  }
}

// 资源未找到错误类
export class NotFoundError extends AppError {
  constructor(resource: string, id?: string) {
    const message = id ? `${resource} with id ${id} not found` : `${resource} not found`;
    super(message, HTTP_STATUS.NOT_FOUND, API_ERROR_CODES.RESOURCE_NOT_FOUND, true);
  }
}

// 冲突错误类
export class ConflictError extends AppError {
  constructor(message: string, details?: any) {
    super(message, HTTP_STATUS.CONFLICT, API_ERROR_CODES.RESOURCE_CONFLICT, true, details);
  }
}

// 限流错误类
export class RateLimitError extends AppError {
  constructor(message: string = 'Too many requests') {
    super(message, HTTP_STATUS.TOO_MANY_REQUESTS, 'TOO_MANY_REQUESTS', true);
  }
}

// 外部服务错误类
export class ExternalServiceError extends AppError {
  constructor(service: string, message: string, details?: any) {
    super(`External service error: ${service} - ${message}`, HTTP_STATUS.BAD_GATEWAY, API_ERROR_CODES.EXTERNAL_SERVICE_ERROR, true, details);
  }
}

// 数据库错误类
export class DatabaseError extends AppError {
  constructor(message: string, details?: any) {
    super(message, HTTP_STATUS.INTERNAL_SERVER_ERROR, API_ERROR_CODES.DATABASE_ERROR, false, details);
  }
}

// 错误处理中间件
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR;
  let errorCode = API_ERROR_CODES.INTERNAL_SERVER_ERROR;
  let message = 'Internal server error';
  let details: any = undefined;

  // 处理自定义错误
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    errorCode = error.code;
    message = error.message;
    details = error.details;
  }
  // 处理Joi验证错误
  else if (error instanceof ValidationError) {
    statusCode = HTTP_STATUS.UNPROCESSABLE_ENTITY;
    errorCode = API_ERROR_CODES.VALIDATION_ERROR;
    message = 'Validation failed';
    details = error.details?.map((detail: any) => ({
      field: detail.path?.join('.'),
      message: detail.message,
      value: detail.context?.value
    }));
  }
  // 处理数据库约束错误
  else if (error.name === 'UniqueViolationError') {
    statusCode = HTTP_STATUS.CONFLICT;
    errorCode = API_ERROR_CODES.DUPLICATE_VALUE;
    message = 'Duplicate value detected';
    details = { constraint: (error as any).constraint };
  }
  // 处理数据库外键错误
  else if (error.name === 'ForeignKeyViolationError') {
    statusCode = HTTP_STATUS.BAD_REQUEST;
    errorCode = API_ERROR_CODES.BUSINESS_RULE_VIOLATION;
    message = 'Referenced resource does not exist';
    details = { constraint: (error as any).constraint };
  }
  // 处理数据库检查约束错误
  else if (error.name === 'CheckViolationError') {
    statusCode = HTTP_STATUS.BAD_REQUEST;
    errorCode = API_ERROR_CODES.BUSINESS_RULE_VIOLATION;
    message = 'Data violates business rules';
    details = { constraint: (error as any).constraint };
  }
  // 处理数据库连接错误
  else if (error.name === 'ConnectionError' || error.message?.includes('ECONNREFUSED')) {
    statusCode = HTTP_STATUS.SERVICE_UNAVAILABLE;
    errorCode = API_ERROR_CODES.DATABASE_ERROR;
    message = 'Database connection failed';
  }
  // 处理JSON解析错误
  else if (error instanceof SyntaxError && 'body' in error) {
    statusCode = HTTP_STATUS.BAD_REQUEST;
    errorCode = API_ERROR_CODES.INVALID_FORMAT;
    message = 'Invalid JSON format';
  }
  // 处理文件上传错误
  else if (error.name === 'MulterError') {
    statusCode = HTTP_STATUS.BAD_REQUEST;
    errorCode = API_ERROR_CODES.FILE_UPLOAD_FAILED;
    message = 'File upload failed';
    details = { type: (error as any).code };
  }
  // 处理超时错误
  else if (error.name === 'TimeoutError') {
    statusCode = HTTP_STATUS.GATEWAY_TIMEOUT;
    errorCode = API_ERROR_CODES.TIMEOUT_ERROR;
    message = 'Request timeout';
  }

  // 记录错误日志
  const logLevel = statusCode >= 500 ? 'error' : 'warn';
  const logData = {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
      statusCode,
      errorCode
    },
    request: {
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body,
      params: req.params,
      query: req.query,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    },
    user: req.user ? {
      id: req.user.id,
      email: req.user.email,
      role: req.user.role
    } : undefined,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  };

  if (logLevel === 'error') {
    logger.error('Request error', logData);
  } else {
    logger.warn('Request warning', logData);
  }

  // 发送错误响应
  const errorResponse: any = {
    success: false,
    error: {
      code: errorCode,
      message,
      ...(details && { details }),
      ...(config.env === 'development' && {
        stack: error.stack,
        name: error.name
      })
    },
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  };

  res.status(statusCode).json(errorResponse);

  // 如果是严重错误，发送到监控系统
  if (statusCode >= 500 && config.monitoring.sentry.dsn) {
    // 这里可以集成Sentry或其他监控服务
    // Sentry.captureException(error, { extra: logData });
  }
};

// 404错误处理中间件
export const notFoundHandler = (req: Request, res: Response): void => {
  const message = `Route ${req.method} ${req.originalUrl} not found`;
  
  logger.warn('Route not found', {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    requestId: req.requestId
  });

  res.status(HTTP_STATUS.NOT_FOUND).json({
    success: false,
    error: {
      code: API_ERROR_CODES.RESOURCE_NOT_FOUND,
      message
    },
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
};

// 异步错误包装器
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 错误工厂函数
export const createError = {
  badRequest: (message: string, details?: any) => 
    new AppError(message, HTTP_STATUS.BAD_REQUEST, API_ERROR_CODES.VALIDATION_ERROR, true, details),
  
  unauthorized: (message: string = 'Unauthorized') => 
    new AuthenticationError(message),
  
  forbidden: (message: string = 'Forbidden') => 
    new AuthorizationError(message),
  
  notFound: (resource: string, id?: string) => 
    new NotFoundError(resource, id),
  
  conflict: (message: string, details?: any) => 
    new ConflictError(message, details),
  
  validation: (message: string, details?: any) => 
    new ValidationError(message, details),
  
  business: (message: string, code: string, details?: any) => 
    new BusinessError(message, code, details),
  
  external: (service: string, message: string, details?: any) => 
    new ExternalServiceError(service, message, details),
  
  database: (message: string, details?: any) => 
    new DatabaseError(message, details),
  
  rateLimit: (message?: string) => 
    new RateLimitError(message),
  
  internal: (message: string = 'Internal server error', details?: any) => 
    new AppError(message, HTTP_STATUS.INTERNAL_SERVER_ERROR, API_ERROR_CODES.INTERNAL_SERVER_ERROR, false, details)
};

// 错误类型检查函数
export const isOperationalError = (error: Error): boolean => {
  if (error instanceof AppError) {
    return error.isOperational;
  }
  return false;
};

// 错误恢复策略
export const handleCriticalError = (error: Error): void => {
  logger.error('Critical error occurred', {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack
    },
    timestamp: new Date().toISOString()
  });

  // 如果是非操作性错误，可能需要重启应用
  if (!isOperationalError(error)) {
    logger.error('Non-operational error detected, consider restarting the application');
    
    // 在生产环境中，可能需要优雅地关闭应用
    if (config.env === 'production') {
      process.exit(1);
    }
  }
};

# 依赖文件
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 构建输出
dist/
build/
out/
.next/
.nuxt/
.vuepress/dist/

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
*.lcov
.nyc_output/

# 依赖目录
jspm_packages/

# TypeScript缓存
*.tsbuildinfo

# 可选的npm缓存目录
.npm

# 可选的eslint缓存
.eslintcache

# 可选的stylelint缓存
.stylelintcache

# Microbundle缓存
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# 可选的REPL历史
.node_repl_history

# 输出的npm包
*.tgz

# Yarn完整性文件
.yarn-integrity

# parcel-bundler缓存
.cache
.parcel-cache

# Next.js构建输出
.next

# Nuxt.js构建/生成输出
.nuxt
dist

# Gatsby文件
.cache/
public

# Storybook构建输出
.out
.storybook-out

# Temporary folders
tmp/
temp/

# 编辑器和IDE
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.swp
*.swo
*~

# OS生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore
docker-compose.override.yml

# Kubernetes
*.kubeconfig

# 数据库
*.sqlite
*.sqlite3
*.db

# 备份文件
*.bak
*.backup
*.sql

# 证书和密钥
*.pem
*.key
*.crt
*.p12
*.pfx

# 测试
test-results/
playwright-report/
test-results.xml
coverage.xml

# 移动端
# React Native
.expo/
.expo-shared/

# Android
*.apk
*.aab
android/app/build/
android/.gradle/
android/local.properties

# iOS
ios/build/
ios/Pods/
*.xcworkspace
*.xcuserdata

# 本地配置
.local
.cache/

# 临时文件
*.tmp
*.temp

# 压缩文件
*.zip
*.tar.gz
*.rar

# 文档生成
docs/build/
docs/.docusaurus/

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Helm
charts/*.tgz

# 监控和日志
prometheus_data/
grafana_data/
elasticsearch_data/

# AI模型文件
*.model
*.pkl
*.joblib
models/

# 上传文件
uploads/
files/
attachments/

# 缓存
.cache/
.tmp/

# 本地开发
.local/
.dev/

# 性能分析
*.cpuprofile
*.heapprofile

# 安全扫描
.snyk

# 包管理器锁文件（根据团队决定是否忽略）
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# 自定义忽略
custom/
private/
secrets/

# 开发指南

## 1. 开发环境搭建

### 1.1 系统要求

#### 1.1.1 硬件要求
- **CPU**: 4核心以上
- **内存**: 16GB以上
- **存储**: 100GB以上可用空间
- **网络**: 稳定的互联网连接

#### 1.1.2 软件要求
```bash
# 必需软件
Node.js 18.0+
Python 3.9+
Docker 20.0+
Docker Compose 2.0+
Git 2.30+

# 推荐软件
VS Code
Postman
DBeaver
```

### 1.2 环境配置

#### 1.2.1 克隆项目
```bash
git clone https://github.com/your-org/link-agent.git
cd link-agent
```

#### 1.2.2 安装依赖
```bash
# 安装根目录依赖
npm install

# 安装各微服务依赖
npm run install:all

# 或者手动安装
cd packages/mcp-core && npm install
cd ../lead-agent && npm install
# ... 其他服务
```

#### 1.2.3 环境变量配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

**环境变量示例**:
```bash
# 数据库配置
DATABASE_URL=postgresql://admin:password@localhost:5432/linkagent
REDIS_URL=redis://localhost:6379
MONGODB_URL=mongodb://localhost:27017/linkagent

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h

# AI服务配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4

# 消息队列配置
RABBITMQ_URL=amqp://guest:guest@localhost:5672

# 日志配置
LOG_LEVEL=debug
LOG_FORMAT=json
```

### 1.3 启动开发环境

#### 1.3.1 启动基础服务
```bash
# 启动数据库和中间件
docker-compose up -d

# 检查服务状态
docker-compose ps
```

#### 1.3.2 启动应用服务
```bash
# 启动所有微服务
npm run dev

# 或者分别启动
npm run dev:mcp      # 主控平台
npm run dev:lead     # 线索智能体
npm run dev:sales    # 销售智能体
npm run dev:web      # Web前端
```

#### 1.3.3 验证环境
```bash
# 检查API健康状态
curl http://localhost:3000/health

# 检查数据库连接
npm run db:check

# 运行测试
npm test
```

## 2. 项目结构

### 2.1 目录结构
```
link-agent/
├── docs/                    # 项目文档
│   ├── architecture.md      # 架构设计
│   ├── api/                 # API文档
│   └── deployment/          # 部署文档
├── packages/               # 微服务包
│   ├── mcp-core/          # 主控平台
│   │   ├── src/
│   │   │   ├── controllers/
│   │   │   ├── services/
│   │   │   ├── models/
│   │   │   └── utils/
│   │   ├── tests/
│   │   └── package.json
│   ├── lead-agent/        # 线索智能体
│   ├── sales-agent/       # 销售智能体
│   └── shared/            # 共享组件
│       ├── types/         # TypeScript类型定义
│       ├── utils/         # 工具函数
│       ├── middleware/    # 中间件
│       └── database/      # 数据库配置
├── web/                   # Web前端
│   ├── src/
│   │   ├── components/    # React组件
│   │   ├── pages/         # 页面组件
│   │   ├── hooks/         # 自定义Hooks
│   │   ├── store/         # 状态管理
│   │   └── utils/         # 工具函数
│   └── public/
├── mobile/                # 移动端应用
├── infrastructure/        # 基础设施配置
│   ├── docker/           # Docker配置
│   ├── kubernetes/       # K8s配置
│   └── terraform/        # 基础设施代码
├── scripts/               # 构建和部署脚本
└── tests/                 # 集成测试
```

### 2.2 命名规范

#### 2.2.1 文件命名
```bash
# 组件文件 - PascalCase
UserProfile.tsx
CustomerList.tsx

# 工具文件 - camelCase
dateUtils.ts
apiClient.ts

# 常量文件 - UPPER_SNAKE_CASE
API_ENDPOINTS.ts
ERROR_CODES.ts

# 配置文件 - kebab-case
docker-compose.yml
eslint.config.js
```

#### 2.2.2 变量命名
```typescript
// 变量和函数 - camelCase
const userName = 'john';
const getUserProfile = () => {};

// 常量 - UPPER_SNAKE_CASE
const API_BASE_URL = 'https://api.example.com';
const MAX_RETRY_COUNT = 3;

// 类和接口 - PascalCase
class UserService {}
interface ApiResponse {}

// 枚举 - PascalCase
enum UserStatus {
  Active = 'active',
  Inactive = 'inactive'
}
```

## 3. 开发规范

### 3.1 代码规范

#### 3.1.1 TypeScript配置
```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "declaration": true,
    "outDir": "./dist",
    "rootDir": "./src"
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "tests"]
}
```

#### 3.1.2 ESLint配置
```json
// .eslintrc.json
{
  "extends": [
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint"],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "warn",
    "prefer-const": "error",
    "no-var": "error"
  }
}
```

#### 3.1.3 Prettier配置
```json
// .prettierrc
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

### 3.2 Git工作流

#### 3.2.1 分支策略
```bash
# 主分支
main          # 生产环境代码
develop       # 开发环境代码

# 功能分支
feature/user-authentication
feature/lead-management
feature/sales-pipeline

# 修复分支
hotfix/critical-bug-fix
bugfix/minor-issue-fix

# 发布分支
release/v1.0.0
release/v1.1.0
```

#### 3.2.2 提交规范
```bash
# 提交消息格式
<type>(<scope>): <subject>

<body>

<footer>

# 示例
feat(auth): add JWT token validation

Implement JWT token validation middleware for API routes.
Add token expiration check and refresh mechanism.

Closes #123
```

**提交类型**:
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 3.3 API设计规范

#### 3.3.1 RESTful API
```typescript
// 资源命名 - 复数名词
GET    /api/v1/customers        # 获取客户列表
POST   /api/v1/customers        # 创建客户
GET    /api/v1/customers/:id    # 获取特定客户
PUT    /api/v1/customers/:id    # 更新客户
DELETE /api/v1/customers/:id    # 删除客户

// 嵌套资源
GET    /api/v1/customers/:id/orders    # 获取客户订单
POST   /api/v1/customers/:id/orders    # 为客户创建订单
```

#### 3.3.2 响应格式
```typescript
// 成功响应
interface ApiResponse<T> {
  success: true;
  data: T;
  message?: string;
  meta?: {
    total?: number;
    page?: number;
    limit?: number;
  };
}

// 错误响应
interface ApiError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
}

// 示例
{
  "success": true,
  "data": {
    "id": "123",
    "name": "John Doe",
    "email": "<EMAIL>"
  },
  "meta": {
    "total": 100,
    "page": 1,
    "limit": 20
  }
}
```

#### 3.3.3 错误处理
```typescript
// 错误代码定义
export const ERROR_CODES = {
  // 认证错误 (1000-1099)
  UNAUTHORIZED: '1001',
  TOKEN_EXPIRED: '1002',
  INVALID_CREDENTIALS: '1003',
  
  // 业务错误 (2000-2099)
  CUSTOMER_NOT_FOUND: '2001',
  DUPLICATE_EMAIL: '2002',
  INVALID_STATUS: '2003',
  
  // 系统错误 (5000-5099)
  INTERNAL_ERROR: '5001',
  DATABASE_ERROR: '5002',
  EXTERNAL_API_ERROR: '5003'
} as const;

// 错误处理中间件
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const statusCode = error.statusCode || 500;
  const errorCode = error.code || ERROR_CODES.INTERNAL_ERROR;
  
  res.status(statusCode).json({
    success: false,
    error: {
      code: errorCode,
      message: error.message,
      ...(process.env.NODE_ENV === 'development' && {
        stack: error.stack
      })
    }
  });
};
```

## 4. 数据库开发

### 4.1 数据库迁移

#### 4.1.1 迁移文件结构
```typescript
// migrations/001_create_users_table.ts
import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('users', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('email').unique().notNullable();
    table.string('password_hash').notNullable();
    table.string('first_name').notNullable();
    table.string('last_name').notNullable();
    table.enum('status', ['active', 'inactive', 'suspended']).defaultTo('active');
    table.jsonb('metadata').defaultTo('{}');
    table.timestamps(true, true);
    
    table.index(['email']);
    table.index(['status']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('users');
}
```

#### 4.1.2 种子数据
```typescript
// seeds/001_users.ts
import { Knex } from 'knex';
import bcrypt from 'bcrypt';

export async function seed(knex: Knex): Promise<void> {
  await knex('users').del();
  
  const users = [
    {
      email: '<EMAIL>',
      password_hash: await bcrypt.hash('admin123', 10),
      first_name: 'Admin',
      last_name: 'User',
      status: 'active'
    }
  ];
  
  await knex('users').insert(users);
}
```

### 4.2 ORM使用

#### 4.2.1 模型定义
```typescript
// models/User.ts
import { Model } from 'objection';
import { BaseModel } from './BaseModel';

export class User extends BaseModel {
  static tableName = 'users';
  
  id!: string;
  email!: string;
  passwordHash!: string;
  firstName!: string;
  lastName!: string;
  status!: 'active' | 'inactive' | 'suspended';
  metadata!: Record<string, any>;
  
  static get jsonSchema() {
    return {
      type: 'object',
      required: ['email', 'passwordHash', 'firstName', 'lastName'],
      properties: {
        id: { type: 'string', format: 'uuid' },
        email: { type: 'string', format: 'email' },
        passwordHash: { type: 'string' },
        firstName: { type: 'string', minLength: 1, maxLength: 50 },
        lastName: { type: 'string', minLength: 1, maxLength: 50 },
        status: { type: 'string', enum: ['active', 'inactive', 'suspended'] },
        metadata: { type: 'object' }
      }
    };
  }
  
  static get relationMappings() {
    return {
      leads: {
        relation: Model.HasManyRelation,
        modelClass: 'Lead',
        join: {
          from: 'users.id',
          to: 'leads.assigned_to'
        }
      }
    };
  }
  
  // 虚拟属性
  get fullName(): string {
    return `${this.firstName} ${this.lastName}`;
  }
  
  // 实例方法
  async verifyPassword(password: string): Promise<boolean> {
    return bcrypt.compare(password, this.passwordHash);
  }
}
```

## 5. 测试开发

### 5.1 测试策略

#### 5.1.1 测试金字塔
```
    /\
   /  \     E2E Tests (10%)
  /____\    
 /      \   Integration Tests (20%)
/________\  Unit Tests (70%)
```

#### 5.1.2 测试类型
- **单元测试**: 测试单个函数或类
- **集成测试**: 测试服务间交互
- **端到端测试**: 测试完整用户流程
- **性能测试**: 测试系统性能指标

### 5.2 单元测试

#### 5.2.1 Jest配置
```json
// jest.config.js
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  testMatch: ['**/__tests__/**/*.ts', '**/?(*.)+(spec|test).ts'],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/index.ts'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};
```

#### 5.2.2 测试示例
```typescript
// tests/services/UserService.test.ts
import { UserService } from '../../src/services/UserService';
import { User } from '../../src/models/User';

describe('UserService', () => {
  let userService: UserService;
  
  beforeEach(() => {
    userService = new UserService();
  });
  
  describe('createUser', () => {
    it('should create a new user with valid data', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe'
      };
      
      const user = await userService.createUser(userData);
      
      expect(user).toBeInstanceOf(User);
      expect(user.email).toBe(userData.email);
      expect(user.fullName).toBe('John Doe');
    });
    
    it('should throw error for duplicate email', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe'
      };
      
      await userService.createUser(userData);
      
      await expect(userService.createUser(userData))
        .rejects
        .toThrow('Email already exists');
    });
  });
});
```

### 5.3 集成测试

#### 5.3.1 API测试
```typescript
// tests/integration/auth.test.ts
import request from 'supertest';
import { app } from '../../src/app';

describe('Authentication API', () => {
  describe('POST /api/v1/auth/login', () => {
    it('should login with valid credentials', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        })
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.data.token).toBeDefined();
      expect(response.body.data.user.email).toBe('<EMAIL>');
    });
    
    it('should reject invalid credentials', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword'
        })
        .expect(401);
      
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INVALID_CREDENTIALS');
    });
  });
});
```

## 6. 部署指南

### 6.1 Docker部署

#### 6.1.1 Dockerfile示例
```dockerfile
# packages/mcp-core/Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine

RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

WORKDIR /app
COPY --from=builder --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --chown=nextjs:nodejs . .

USER nextjs

EXPOSE 3000

CMD ["npm", "start"]
```

#### 6.1.2 Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  mcp-core:
    build: ./packages/mcp-core
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
    depends_on:
      - postgres
      - redis
    
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: linkagent
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### 6.2 Kubernetes部署

#### 6.2.1 部署配置
```yaml
# k8s/mcp-core-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mcp-core
  labels:
    app: mcp-core
spec:
  replicas: 3
  selector:
    matchLabels:
      app: mcp-core
  template:
    metadata:
      labels:
        app: mcp-core
    spec:
      containers:
      - name: mcp-core
        image: linkagent/mcp-core:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
```

## 7. 监控和调试

### 7.1 日志管理

#### 7.1.1 日志配置
```typescript
// src/utils/logger.ts
import winston from 'winston';

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'mcp-core' },
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

export default logger;
```

### 7.2 性能监控

#### 7.2.1 APM集成
```typescript
// src/middleware/monitoring.ts
import { Request, Response, NextFunction } from 'express';
import { performance } from 'perf_hooks';
import logger from '../utils/logger';

export const performanceMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const start = performance.now();
  
  res.on('finish', () => {
    const duration = performance.now() - start;
    
    logger.info('Request completed', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: Math.round(duration),
      userAgent: req.get('User-Agent')
    });
  });
  
  next();
};
```

这份开发指南为Link Agent项目提供了完整的开发规范和最佳实践。开发团队可以按照这个指南进行项目开发，确保代码质量和开发效率。
